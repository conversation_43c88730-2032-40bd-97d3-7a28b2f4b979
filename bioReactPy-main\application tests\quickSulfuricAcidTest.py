"""
快速硫酸矿化反应测试

这是一个简化版本的硫酸矿化反应模拟，用于快速测试和验证化学反应机制。

化学反应:
1. H₂SO₄ → 2H⁺ + SO₄²⁻                    (完全电离)
2. CaCO₃ + 2H⁺ → Ca²⁺ + CO₂ + H₂O         (方解石溶解)
3. CaSiO₃ + 2H⁺ → Ca²⁺ + H₂SiO₃           (硅灰石溶解)
4. Ca²⁺ + H₄SiO₄ → CaSiO₃ + 2H₂O + 2H⁺    (硅酸钙沉淀)
5. Ca²⁺ + SO₄²⁻ + 2H₂O → CaSO₄·2H₂O       (石膏沉淀)
"""

import numpy as np
import math
import sys
import os

# Add the src directory to Python path
src_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src')
sys.path.insert(0, src_path)

from geometry import structured
from geometry.bc import BoundaryCondition, BoundaryConditionTransport
from importExport import readf
import time

def quick_test():
    print("=== 快速硫酸矿化反应测试 ===")
    
    # 简化的几何参数
    factor = 0.2
    Lx = 0.3 * factor
    Ly = 0.03 * factor
    N = 3  # 非常小的网格
    dx = 0.01
    Nx = round(Lx / dx)
    Ny = round(Ly / dx)
    
    print(f"网格尺寸: Nx={Nx}, Ny={Ny}")
    
    domain = np.array([Lx, Ly])
    basedim = np.array([Nx, Ny])
    
    # 创建网格
    g = structured.CartGrid(basedim, domain)
    g.compute_geometry()
    
    print(f"总网格数: {g.num_cells}")
    
    # 化学参数
    gammaH = 1000
    gammaCa = 1000
    gammaSO4 = 1000
    gammaSiO2 = 1000
    
    # 平衡常数
    logKeqCaCO3 = -8.48
    logKeqGypsum = -4.6
    Ksp_calcite = math.pow(10, logKeqCaCO3)
    Ksp_gypsum = math.pow(10, logKeqGypsum)
    
    # 动力学参数 (增加反应速率以观察效果)
    kappaDiss_calcite = 1e-6      # 增加溶解速率
    kappaDiss_wollastonite = 1e-6  # 增加溶解速率
    kappaPrec_CSH = 1e-8          # 增加沉淀速率
    kappaPrec_gypsum = 1e-7       # 增加沉淀速率
    
    # 摩尔体积
    VmCalcite = 36.9
    VmWollastonite = 39.83
    VmGypsum = 74.7
    
    # 初始条件
    pH_init = 7.0
    cH_init = math.pow(10, -pH_init) / gammaH
    cCa_init = 1e-6
    cSO4_init = 1e-6
    cSi_init = 1e-6
    cCO2_init = 1e-6
    
    # 边界条件 (硫酸注入) - 增加浓度以促进反应
    cH2SO4_bnd = 0.005   # 增加硫酸浓度
    cH_bnd = 2 * cH2SO4_bnd
    cSO4_bnd = cH2SO4_bnd
    pH_bnd = -math.log10(cH_bnd * gammaH)
    
    print(f"边界pH: {pH_bnd:.2f}")
    print(f"边界H⁺浓度: {cH_bnd:.2e}")
    
    # 初始化浓度场
    cH = cH_init * np.ones(g.num_cells)
    cCa = cCa_init * np.ones(g.num_cells)
    cSO4 = cSO4_init * np.ones(g.num_cells)
    cSi = cSi_init * np.ones(g.num_cells)
    cCO2 = cCO2_init * np.ones(g.num_cells)
    
    # 初始化矿物相
    phi_calcite = np.zeros(g.num_cells)
    phi_wollastonite = 0.1 * np.ones(g.num_cells)  # 10%硅灰石
    phi_CSH = np.zeros(g.num_cells)
    phi_gypsum = np.zeros(g.num_cells)
    
    phi_total_solid = phi_calcite + phi_wollastonite + phi_CSH + phi_gypsum
    phi = 1 - phi_total_solid
    
    print(f"初始孔隙度: {np.mean(phi):.3f}")
    print(f"初始硅灰石体积分数: {np.mean(phi_wollastonite):.3f}")
    
    # 时间参数
    dt = 0.01
    t_end = 2.0
    t = 0
    
    # 监测数据
    times = []
    pH_values = []
    Ca_values = []
    SO4_values = []
    Si_values = []
    calcite_volumes = []
    wollastonite_volumes = []
    CSH_volumes = []
    gypsum_volumes = []
    
    print("\n开始时间循环...")
    
    iteration = 0
    while t <= t_end:
        iteration += 1
        
        # 简化的边界条件应用 (只在边界网格应用)
        if g.num_cells > 0:
            # 左边界注入硫酸
            boundary_cells = slice(0, min(5, g.num_cells))
            cH[boundary_cells] = cH_bnd
            cSO4[boundary_cells] = cSO4_bnd
        
        # 计算反应速率
        sigma = np.ones(g.num_cells) * 1.0  # 增加表面积密度
        
        # 溶解反应
        rDiss_calcite = kappaDiss_calcite * sigma * (cH**2) * phi_calcite * VmCalcite
        rDiss_wollastonite = kappaDiss_wollastonite * sigma * (cH**2) * phi_wollastonite * VmWollastonite
        
        # 沉淀反应
        cH4SiO4 = cSi  # 简化
        cCO3 = cCO2 * 1e-10 / (cH * cH + 1e-20)  # 简化平衡
        
        # CSH沉淀
        SI_CSH = (gammaCa * cCa * gammaSiO2 * cH4SiO4) / 1e-10
        rPrec_CSH = np.where(SI_CSH > 1, 
                            kappaPrec_CSH * sigma * (SI_CSH - 1) * VmWollastonite, 
                            0)
        
        # 石膏沉淀
        SI_gypsum = (gammaCa * cCa * gammaSO4 * cSO4) / Ksp_gypsum
        rPrec_gypsum = np.where(SI_gypsum > 1,
                               kappaPrec_gypsum * sigma * (SI_gypsum - 1) * VmGypsum,
                               0)
        
        # 更新矿物相
        phi_calcite = np.maximum(phi_calcite - dt * rDiss_calcite, 0)
        phi_wollastonite = np.maximum(phi_wollastonite - dt * rDiss_wollastonite, 0)
        phi_CSH = phi_CSH + dt * rPrec_CSH
        phi_gypsum = phi_gypsum + dt * rPrec_gypsum
        
        # 确保物理约束
        phi_total_solid = phi_calcite + phi_wollastonite + phi_CSH + phi_gypsum
        
        # 防止过填充
        overfill = phi_total_solid > 0.99
        if np.any(overfill):
            scale = 0.99 / phi_total_solid[overfill]
            phi_calcite[overfill] *= scale
            phi_wollastonite[overfill] *= scale
            phi_CSH[overfill] *= scale
            phi_gypsum[overfill] *= scale
            phi_total_solid = phi_calcite + phi_wollastonite + phi_CSH + phi_gypsum
        
        phi = 1 - phi_total_solid
        
        # 简化的浓度更新 (基于反应化学计量)
        # H⁺消耗于溶解反应，产生于沉淀反应
        dcH_dt = -2 * (rDiss_calcite/VmCalcite + rDiss_wollastonite/VmWollastonite) + 2 * rPrec_CSH/VmWollastonite
        cH = np.maximum(cH + dt * dcH_dt, 1e-12)
        
        # Ca²⁺产生于溶解，消耗于沉淀
        dcCa_dt = (rDiss_calcite/VmCalcite + rDiss_wollastonite/VmWollastonite) - (rPrec_CSH/VmWollastonite + rPrec_gypsum/VmGypsum)
        cCa = np.maximum(cCa + dt * dcCa_dt, 1e-12)
        
        # Si产生于硅灰石溶解，消耗于CSH沉淀
        dcSi_dt = rDiss_wollastonite/VmWollastonite - rPrec_CSH/VmWollastonite
        cSi = np.maximum(cSi + dt * dcSi_dt, 1e-12)
        
        # CO₂产生于方解石溶解
        dcCO2_dt = rDiss_calcite/VmCalcite
        cCO2 = cCO2 + dt * dcCO2_dt
        
        # 记录数据
        if iteration % 10 == 0:
            times.append(t)
            pH_values.append(-np.log10(np.mean(cH) * gammaH))
            Ca_values.append(np.mean(cCa))
            SO4_values.append(np.mean(cSO4))
            Si_values.append(np.mean(cSi))
            calcite_volumes.append(np.sum(phi_calcite * g.cell_volumes))
            wollastonite_volumes.append(np.sum(phi_wollastonite * g.cell_volumes))
            CSH_volumes.append(np.sum(phi_CSH * g.cell_volumes))
            gypsum_volumes.append(np.sum(phi_gypsum * g.cell_volumes))
            
            print(f"t={t:.2f}, pH={pH_values[-1]:.2f}, Ca={Ca_values[-1]:.2e}, CSH_vol={CSH_volumes[-1]:.6f}")
        
        t += dt
    
    print("\n=== 模拟完成 ===")
    print(f"总迭代次数: {iteration}")
    print(f"最终pH: {pH_values[-1]:.2f}")
    print(f"最终Ca²⁺浓度: {Ca_values[-1]:.2e}")
    print(f"最终硅灰石体积: {wollastonite_volumes[-1]:.6f}")
    print(f"最终CSH体积: {CSH_volumes[-1]:.6f}")
    print(f"最终石膏体积: {gypsum_volumes[-1]:.6f}")
    
    # 验证质量守恒
    initial_wollastonite = wollastonite_volumes[0]
    final_wollastonite = wollastonite_volumes[-1]
    dissolved_wollastonite = initial_wollastonite - final_wollastonite
    formed_CSH = CSH_volumes[-1]
    
    print(f"\n质量平衡检查:")
    print(f"溶解的硅灰石: {dissolved_wollastonite:.6f}")
    print(f"形成的CSH: {formed_CSH:.6f}")
    print(f"转化效率: {(formed_CSH/dissolved_wollastonite*100):.1f}%" if dissolved_wollastonite > 0 else "N/A")
    
    return {
        'times': times,
        'pH': pH_values,
        'Ca': Ca_values,
        'SO4': SO4_values,
        'Si': Si_values,
        'calcite_volumes': calcite_volumes,
        'wollastonite_volumes': wollastonite_volumes,
        'CSH_volumes': CSH_volumes,
        'gypsum_volumes': gypsum_volumes
    }

if __name__ == "__main__":
    start_time = time.time()
    results = quick_test()
    end_time = time.time()
    
    print(f"\n总运行时间: {end_time - start_time:.2f} 秒")
    print("快速测试完成！")
