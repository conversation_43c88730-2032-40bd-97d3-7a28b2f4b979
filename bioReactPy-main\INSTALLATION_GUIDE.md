# BioReactPy 安装和运行指南

## 系统要求
- Python 3.10 或更高版本
- Windows/Linux/macOS

## 安装步骤

### 1. 安装Python依赖库
```bash
# 升级pip
python -m pip install --upgrade pip

# 安装所需库
python -m pip install -r requirements.txt
```

或者手动安装：
```bash
python -m pip install numpy>=1.21.0
python -m pip install scipy>=1.7.0
python -m pip install xlsxwriter>=3.0.0
python -m pip install xlrd>=2.0.0
python -m pip install openpyxl>=3.1.0
python -m pip install matplotlib>=3.5.0
```

### 2. 创建输出目录
在运行测试之前，需要创建输出目录：

**Windows PowerShell:**
```powershell
New-Item -ItemType Directory -Path "application tests/outputFiles/plots", "application tests/outputFiles/fluxes", "application tests/outputFiles/concentrations", "application tests/outputFiles/monitors", "application tests/outputFiles/residuals" -Force
```

**Linux/macOS:**
```bash
mkdir -p "application tests/outputFiles/"{plots,fluxes,concentrations,monitors,residuals}
```

### 3. 运行测试

#### 步骤1: 生成流场数据
首先运行流场计算：
```bash
cd "application tests"
python soulaineFlow.py
```

#### 步骤2: 运行硫酸矿化测试
```bash
python sulfuricAcidMineralizationTest.py
```

## 成功安装验证

如果安装成功，您应该看到：

1. **soulaineFlow.py** 输出：
   - 网格信息：`Nx 480 Ny 40 dx 0.0025`
   - 迭代过程和收敛信息
   - 最终：`elapsed time X.X seconds`

2. **sulfuricAcidMineralizationTest.py** 输出：
   - 反应参数信息
   - 模拟进度：`Progress: X.X% (t=X.X/5)`
   - 化学浓度变化
   - 最终：`elapsed time in hours X h X m X s`

## 常见问题解决

### 问题1: ModuleNotFoundError
**错误**: `ModuleNotFoundError: No module named 'numpy'`
**解决**: 确保已安装所有依赖库，运行 `pip install -r requirements.txt`

### 问题2: 文件路径错误
**错误**: `FileNotFoundError: outputFiles/...`
**解决**: 确保在正确的目录中运行，并创建了输出目录

### 问题3: Excel文件读取错误
**错误**: `xlrd.biffh.XLRDError: Excel xlsx file; not supported`
**解决**: 确保安装了openpyxl库：`pip install openpyxl`

### 问题4: 数组索引类型错误 (已修复)
**错误**: `IndexError: arrays used as indices must be of integer (or boolean) type`
**解决**: 此问题已在sulfuricAcidMineralizationTest.py中修复，确保使用最新版本的代码

## 已验证的库版本
- numpy: 2.2.6
- scipy: 1.15.3
- matplotlib: 3.10.3
- xlsxwriter: 3.2.5
- xlrd: 2.0.2
- openpyxl: 3.1.5

## 项目结构
```
bioReactPy-main/
├── src/                    # 源代码
├── application tests/      # 应用测试
│   ├── outputFiles/       # 输出文件目录
│   ├── soulaineFlow.py    # 流场计算
│   └── sulfuricAcidMineralizationTest.py  # 硫酸矿化测试
├── examples/              # 示例代码
├── benchmarks/            # 基准测试
└── requirements.txt       # 依赖库列表
```

## 支持
如果遇到问题，请检查：
1. Python版本是否为3.10+
2. 所有依赖库是否正确安装
3. 输出目录是否存在
4. 是否按正确顺序运行测试文件
