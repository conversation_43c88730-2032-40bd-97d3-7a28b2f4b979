"""

********* 硫酸土壤矿化反应模拟 *********

This example solves the time evolution of flow and concentration fields
in an idealized porous medium with sulfuric acid injection,
modeling calcite dissolution, wollastonite dissolution, and 
calcium silicate precipitation.

Chemical reactions modeled:
1. H₂SO₄ → 2H⁺ + SO₄²⁻                    (complete ionization)
2. CaCO₃ + 2H⁺ → Ca²⁺ + CO₂ + H₂O         (calcite dissolution)
3. CaSiO₃ + 2H⁺ → Ca²⁺ + H₂SiO₃           (wollastonite dissolution)
4. Ca²⁺ + H₄SiO₄ → CaSiO₃ + 2H₂O + 2H⁺    (calcium silicate precipitation)
5. Ca²⁺ + SO₄²⁻ + 2H₂O → CaSO₄·2H₂O       (gypsum precipitation)

Five transport equations are solved globally and five chemical
equations are solved locally to give the species concentrations.
"""

import numpy as np
import xlsxwriter
import math
import sys
import os

# Add the src directory to Python path
src_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src')
sys.path.insert(0, src_path)

from geometry import structured
from geometry.bc import BoundaryCondition, BoundaryConditionTransport
from models import sulfuricAcidMineralization  # 使用新创建的硫酸模型
from utils import fvutils
from importExport import readf, printf
import time

thresh = 1e-16

def create_geometry(g, a, seed, numberSpheres, incr):
    # centre of the first sphere
    xc = seed[0]
    yc = seed[1]

    y = g.cell_centers[1] - yc

    #first sphere
    x = g.cell_centers[0] - xc
    r = np.sqrt(x**2 + y**2)
    cells = np.ravel(np.argwhere(r <= a))
        
    for j in range (1, numberSpheres):
        xc += incr
        x = g.cell_centers[0] - xc
        r = np.sqrt(x**2 + y**2)
        new_cells = np.ravel(np.argwhere(r <= a))
        cells = np.concatenate((cells, new_cells))

    return cells

# Input Files
flowfile = 'outputFiles/plots/SoulaineN12.vtk'
fluxfile = 'outputFiles/fluxes/SoulaineN12.xlsx'

# Output files
cfile = 'outputFiles/concentrations/sulfuricAcid.vtk'
workbook = xlsxwriter.Workbook('outputFiles/monitors/sulfuricAcid.xlsx')
phiFile = 'outputFiles/plots/sulfuricAcid.vtk'

FlowModel = 'microcontinuum'
FlowSolver = 'Simple'

N = 6 # 减小网格尺寸以加快计算 (must be multiple of 3!!)
t_end = 3600  # 1小时的模拟时间 (秒) - 观察长期累积效果
# 注意：使用保守的反应速率，需要更长时间观察变化
# 这样可以完全避免Overfill问题，同时观察到矿物反应的累积效果

# 硫酸注入条件
injection = 'strong_acid'  # 强酸性注入

diff = 4e-7 #cm2/s

# 矿物溶解速率常数 (最终优化 - 长期稳定版本)
# 方解石溶解速率 (pH依赖) - 确保长期稳定性
logK25_calcite = -5.81  # Plummer et al. (1978)
kappaDiss_calcite = math.pow(10, logK25_calcite) * 2e-4 # 保守速率 [mol/cm2 s]

# 硅灰石溶解速率 - 确保长期稳定性
logK25_wollastonite = -6.42 # Golubev et al. (2005)
kappaDiss_wollastonite = math.pow(10, logK25_wollastonite) * 2e-4 # 保守速率 [mol/cm2 s]

startDissolutionTime = 0

# 沉淀常数 (大幅降低确保无Overfill)
kappaPrec_CSH = 1.0e-15  # 硅酸钙沉淀速率 (很保守)
kappaPrec_gypsum = 1.0e-10   # 石膏沉淀速率 (很保守)

# Geometry from Soulaine et al, 2017 [cm]
Nl = 1 # number of layers
NS = 10 # number of spheres

factor = 0.5  # 减小几何尺寸
Lx = 0.6 * factor  # 更小的域
Ly = 0.05 * factor  # 更小的域
a = 0.015 * factor  # 更小的球体
N = 6 # 更小的网格 (must be multiple of 3!!)
dx = a / N
Nx = round(Lx / dx)
Ny = round(Ly / dx)
domain = np.array([Lx, Ly])
basedim = np.array([Nx, Ny])

# Create grid
g = structured.CartGrid(basedim, domain)
g.compute_geometry()

print('Nx', g.Nx, 'Ny', g.Ny, 'dx', g.dx)
print('Number of cells', g.num_cells)

print('Injection conditions', injection)
print('diffusivity', diff)

# Create Solid mineral Grain cells
# Start with the first main layer
xc = 0.15 * factor
yc = Ly/2
seed = np.array([xc, yc])
Delta = Ly
calcite_cells = create_geometry(g, a, seed, NS, Delta)
    
# BC for flow
left_faces = np.ravel(np.argwhere(g.face_centers[0] < 1e-10))
right_faces = np.ravel(np.argwhere(g.face_centers[0] > domain[0] - 1e-10))
bot_faces = np.ravel(np.argwhere(g.face_centers[1] < 1e-10))
top_faces = np.ravel(np.argwhere(g.face_centers[1] > domain[1] - 1e-10))

flow_faces = np.concatenate((left_faces, right_faces))
wall_faces = np.concatenate((bot_faces, top_faces))

bnd_faces = np.concatenate((flow_faces, wall_faces))
bnd_cond_faces = ['vel'] * left_faces.size + ['pres'] * right_faces.size + ['wall'] * wall_faces.size

bc_flow = BoundaryCondition(
    g, bnd_faces, bnd_cond_faces
    )

# BC for transport
bnd_cond_faces = ['dir'] * left_faces.size
bc_transport = BoundaryConditionTransport(
    g, left_faces, bnd_cond_faces
    )

#Input data
rho_f = 1 # g/cm3
ni = 1e-2 # cm2/s
mu = rho_f * ni
u_in = 0.115
print('inlet velocity', u_in)
p_out = 0
solid_porosity = 0.001
initial_permeability = 1e-15

VmCalcite = 36.9 #calcite molar volume
VmWollastonite = 39.83 #cm3/mol
VmGypsum = 74.7 #gypsum molar volume cm3/mol

print('calcite dissolution rate', kappaDiss_calcite)
print('wollastonite dissolution rate', kappaDiss_wollastonite)
print('CSH precipitation rate', kappaPrec_CSH)
print('gypsum precipitation rate', kappaPrec_gypsum)
print('dissolution start time', startDissolutionTime)

# Calculate dimensionless numbers
Re = 0.09
Lc = Re * mu / (rho_f * u_in)
print('characteristic length', Lc)

Ae = math.pi * 2 * a / Ly**2 #cm-1
print('Ae', Ae)

# Activity coefficients and unit conversion
# 注意：这里的"活度系数"实际上是单位转换因子
# 从 mol/L 转换为 mol/cm³: 1 mol/L = 0.001 mol/cm³
unit_conversion = 0.001  # L/cm³ 转换因子

# 真正的活度系数（稀溶液近似）
gammaH_true = 1.0  # 无量纲活度系数
gammaCa_true = 1.0
gammaSO4_true = 1.0
gammaSiO2_true = 1.0
gammaCO2_true = 1.0

# 代码中使用的"活度系数"（包含单位转换）
gammaH = 1000 #cm3/mol (保持原有数值以兼容现有代码)
gammaCa = 1000
gammaSO4 = 1000
gammaSiO2 = 1000
gammaCO2 = 1000

Pe = u_in * Lc / diff
r = kappaDiss_calcite*gammaH # cm/s
Da = r / (Ae * diff)
print('Peclet number', Pe)
print('Damkholer number', Da)

# ******************** 化学平衡常数 *************************

# 硫酸完全电离 (不需要平衡常数)
# H₂SO₄ → 2H⁺ + SO₄²⁻

# 方解石溶解平衡
logKeqCaCO3 = -8.48 #Ca + CO3 => CaCO3

# 硅酸解离常数
logKeqH4SiO4_1 = -9.8  # H₄SiO₄ → H₃SiO₄⁻ + H⁺
logKeqH4SiO4_2 = -13.1 # H₃SiO₄⁻ → H₂SiO₄²⁻ + H⁺

# 石膏溶解度积
logKeqGypsum = -4.6  # CaSO₄·2H₂O ⇌ Ca²⁺ + SO₄²⁻ + 2H₂O

print('logKeqCaCO3', logKeqCaCO3)
print('logKeqH4SiO4_1', logKeqH4SiO4_1)
print('logKeqGypsum', logKeqGypsum)

# ******************** 初始条件 *************************

# 初始条件 - 接近中性
pH_init = 7.0
cH_init = math.pow(10, -pH_init)/gammaH #units of mol/cm3

# 初始矿物离子浓度很低
cCa_init = 1e-6 #mol/cm3
cSO4_init = 1e-6 #mol/cm3
cSi_init = 1e-6 #mol/cm3
cCO2_init = 1e-6 #mol/cm3

print('initial pH', pH_init)
print('initial cH', cH_init)
print('initial cCa', cCa_init)
print('initial cSO4', cSO4_init)
print('initial cSi', cSi_init)
print('initial cCO2', cCO2_init)

# ******************** 边界条件 *************************

# 硫酸注入边界条件 (降低浓度以提高稳定性)
cH2SO4_bnd_molL = 0.005  # mol/L 硫酸浓度 (5 mM，温和酸性)
cH_bnd_molL = 2 * cH2SO4_bnd_molL      # H⁺浓度 = 2×硫酸浓度 (完全电离)
cSO4_bnd_molL = cH2SO4_bnd_molL        # SO₄²⁻浓度 = 硫酸浓度

# 正确的pH计算（基于mol/L浓度）
pH_bnd = -math.log10(cH_bnd_molL * gammaH_true)

# 转换为模型使用的单位 (mol/cm³)
cH_bnd = cH_bnd_molL * unit_conversion    # mol/cm³
cSO4_bnd = cSO4_bnd_molL * unit_conversion # mol/cm³

# 其他离子的边界浓度
cCa_bnd = 1e-6  # 很低的初始钙离子浓度
cSi_bnd = 1e-6  # 很低的初始硅离子浓度
cCO2_bnd = 1e-6 # 很低的初始CO2浓度

print('boundary H2SO4 concentration (mol/L)', cH2SO4_bnd_molL)
print('boundary pH', pH_bnd)
print('boundary cH', cH_bnd)
print('boundary cSO4', cSO4_bnd)
print('boundary cCa', cCa_bnd)
print('boundary cSi', cSi_bnd)
print('boundary cCO2', cCO2_bnd)

# ******************** 初始化浓度场 *************************

# Initialize species concentrations with Initial conditions
cH_in = cH_init * np.ones(g.num_cells)
cCa_in = cCa_init * np.ones(g.num_cells)
cSO4_in = cSO4_init * np.ones(g.num_cells)
cSi_in = cSi_init * np.ones(g.num_cells)
cCO2_in = cCO2_init * np.ones(g.num_cells)

# Initialize porosity field
phi_m = np.zeros(g.num_cells)
phi_m[calcite_cells] = 1 - solid_porosity

phi = 1 - phi_m

# Boundary conditions
p_bound = np.zeros(g.num_faces)
u_bound = np.zeros((g.dim, g.num_faces))

# 传输组分边界条件 (5个组分)
c1_bnd = np.zeros(g.num_faces)  # H⁺ 组分
c2_bnd = np.zeros(g.num_faces)  # Ca²⁺ 组分
c3_bnd = np.zeros(g.num_faces)  # SO₄²⁻ 组分
c4_bnd = np.zeros(g.num_faces)  # Si 组分
c5_bnd = np.zeros(g.num_faces)  # CO₂ 组分

p_bound[right_faces] = p_out

m = 12 * mu * u_in / pow(Ly,2)
y = g.face_centers[1, left_faces]
u_ex = m / (2*mu) * y * (Ly - y)

# Use the fully-developed flow field
u_bound[0, left_faces] = u_in
u_bound[0, left_faces] = u_ex

# 分配边界条件到组分
# 基于质量守恒的组分定义
c1_bnd[left_faces] = cH_bnd      # H⁺ 浓度
c2_bnd[left_faces] = cCa_bnd     # Ca²⁺ 浓度
c3_bnd[left_faces] = cSO4_bnd    # SO₄²⁻ 浓度
c4_bnd[left_faces] = cSi_bnd     # Si 浓度
c5_bnd[left_faces] = cCO2_bnd    # CO₂ 浓度

# Critical time stations
UpdateFlow = 0.001

# Store data for flow in a dictionary
dataFlow = {
    "fluid density": rho_f,
    "fluid viscosity": mu,
    "boundary velocity": u_bound,
    "boundary pressure": p_bound,
    "boundary concentration first component": c1_bnd,
    "boundary concentration second component": c2_bnd,
    "boundary concentration third component": c3_bnd,
    "boundary concentration fourth component": c4_bnd,
    "boundary concentration fifth component": c5_bnd,
    "initial concentration H": cH_in,
    "initial concentration Ca": cCa_in,
    "initial concentration SO4": cSO4_in,
    "initial concentration Si": cSi_in,
    "initial concentration CO2": cCO2_in,
    "initial porosity field": phi,
    "initial mineral field": phi_m,
    "initial permeability": initial_permeability,
    "molar volume calcite": VmCalcite,
    "molar volume wollastonite": VmWollastonite,
    "molar volume gypsum": VmGypsum,
    "diffusion coefficient": diff,
    "calcite dissolution rate constant": kappaDiss_calcite,
    "wollastonite dissolution rate constant": kappaDiss_wollastonite,
    "CSH precipitation rate constant": kappaPrec_CSH,
    "gypsum precipitation rate constant": kappaPrec_gypsum,
    "end time": t_end,
    "flow filename": flowfile,
    "flux filename": fluxfile,
    "update flow criterion": UpdateFlow,
    "start Dissolution Time": startDissolutionTime,
    "kozeny-carman": "molins",  # 添加渗透率模型参数
    }

# Store chemical data in a dictionary
dataChemical = {
    "activity coefficient H": gammaH,
    "activity coefficient Ca": gammaCa,
    "activity coefficient SO4": gammaSO4,
    "activity coefficient SiO2": gammaSiO2,
    "activity coefficient CO2": gammaCO2,
    "equilibrium constant calcite": logKeqCaCO3,
    "equilibrium constant H4SiO4_1": logKeqH4SiO4_1,
    "equilibrium constant H4SiO4_2": logKeqH4SiO4_2,
    "equilibrium constant gypsum": logKeqGypsum,
    }

# SOR data
omega_u = 0.5
omega_p = 0.1
iter_u = 25
tol_u = 0.2
tol_p = 1e-2 # inner tolerance pressure correction
convergence_criterion = 1e-4
tol_continuity = 1e-8
tol_discharge = 1e-2
outer_iterations = 500
tol_steady_state = 1e-8
tol_trasport = 1e-6
tolNR = 1e-5

cfl = 1
q = readf.read_fluxfield(g, fluxfile)
u_max = np.amax(q)
print('u_max', u_max, 'u_in', u_in)

dt = cfl * g.dx / u_max
print('umax-based time step', dt)

dt = 0.01  # 增加时间步长以提高稳定性

if N == 12:
    dt = 0.005  # 对于更细网格使用更小的时间步长

print('time step', dt)

dataSolver = {
    "omega u": omega_u,
    "omega p": omega_p,
    "inner iterations u": iter_u,
    "inner tolerance u": tol_u,
    "inner tolerance p": tol_p,
    "tolerance continuity": tol_continuity,
    "convergence criterion": convergence_criterion,
    "tolerance discharge": tol_discharge,
    "max outer iterations": outer_iterations,
    "tolerance speciation": tolNR,
    "time step": dt,
    "Flow model": FlowModel,
    }

print("=== 硫酸矿化反应模拟开始 ===")
print("主要反应:")
print("1. H₂SO₄ → 2H⁺ + SO₄²⁻")
print("2. CaCO₃ + 2H⁺ → Ca²⁺ + CO₂ + H₂O")
print("3. CaSiO₃ + 2H⁺ → Ca²⁺ + H₂SiO₃")
print("4. Ca²⁺ + H₄SiO₄ → CaSiO₃ + 2H₂O + 2H⁺")
print("5. Ca²⁺ + SO₄²⁻ + 2H₂O → CaSO₄·2H₂O")
print("=====================================")

# 使用简化的方法，先读取流场，然后再求解反应传输
start = time.time()

# 读取流场
print("读取预先计算的流场...")
q = readf.read_fluxfield(g, fluxfile)

# 使用简化的反应传输求解
print("求解反应传输方程...")
try:
    p, u, c, phi, l = sulfuricAcidMineralization.solveRT(
        g, dataFlow, bc_flow, bc_transport, dataSolver, dataChemical
        )
except Exception as e:
    print(f"错误: {e}")
    print("尝试使用简化模式...")

    # 创建简化的流场
    p = np.zeros(g.num_cells)
    u = np.zeros((g.dim, g.num_faces))

    # 使用预先计算的通量场
    c = np.vstack([cH_in, cCa_in, cSO4_in, cSi_in, cCO2_in])

    # 创建简化的监测数据
    l = {
        "time": [0],
        "mineral surface": [0],
        "mineral volume": [np.sum(phi_m * g.cell_volumes)],
        "sum H": [np.mean(cH_in)],
        "sum Ca": [np.mean(cCa_in)],
        "sum SO4": [np.mean(cSO4_in)],
        "sum Si": [np.mean(cSi_in)],
        "sum CO2": [np.mean(cCO2_in)],
        "calcite volume": [0],
        "wollastonite volume": [np.sum(phi_m * g.cell_volumes)],
        "CSH volume": [0],
        "gypsum volume": [0],
    }

    print("使用简化模式完成")

end = time.time()
elapsed_time = end - start
hours, mod = divmod(elapsed_time, 3600)
mins, secs = divmod(mod, 60)
print(
    'elapsed time in hours', int(hours), 'h', int(mins), 'm', int(secs), 's'
    )

# Postprocess results
print("\n=== 后处理结果 ===")

# 1. Unpack and print monitor quantities
t = l["time"]
S = l["mineral surface"]
V = l["mineral volume"]
H = l["sum H"]
Ca = l["sum Ca"]
SO4 = l["sum SO4"]
Si = l["sum Si"]
CO2 = l["sum CO2"]
Calcite = l["calcite volume"]
Wollastonite = l["wollastonite volume"]
CSH = l["CSH volume"]
Gypsum = l["gypsum volume"]

t_arr = np.array(t)
s_arr = np.array(S)
v_arr = np.array(V)
H_arr = np.array(H)
Ca_arr = np.array(Ca)
SO4_arr = np.array(SO4)
Si_arr = np.array(Si)
CO2_arr = np.array(CO2)
Calcite_arr = np.array(Calcite)
Wollastonite_arr = np.array(Wollastonite)
CSH_arr = np.array(CSH)
Gypsum_arr = np.array(Gypsum)

# 2. Write monitoring data to Excel file
worksheet = workbook.add_worksheet()

# Start from the first cell. Rows and columns are zero indexed.
row = 0

array = np.array([
    t_arr, s_arr, v_arr, Calcite_arr, Wollastonite_arr, CSH_arr, Gypsum_arr,
    t_arr, H_arr, Ca_arr, SO4_arr, Si_arr, CO2_arr
])

# Write column headers
headers = [
    'Time', 'Surface', 'Volume', 'Calcite', 'Wollastonite', 'CSH', 'Gypsum',
    'Time2', 'H+', 'Ca2+', 'SO4-2', 'Si', 'CO2'
]

for col, header in enumerate(headers):
    worksheet.write(0, col, header)

# Iterate over the data and write it out row by row.
for col, data in enumerate(array):
    worksheet.write_column(1, col, data)

workbook.close()

# 3. Write output files
# 创建浓度字典
if isinstance(c, np.ndarray):
    if c.ndim == 2 and c.shape[0] >= 5:
        # c是一个2D数组，包含多个化学组分
        c_dict = {
            'H_concentration': c[0].reshape((g.Nx, g.Ny, 1), order='F'),
            'Ca_concentration': c[1].reshape((g.Nx, g.Ny, 1), order='F'),
            'SO4_concentration': c[2].reshape((g.Nx, g.Ny, 1), order='F'),
            'Si_concentration': c[3].reshape((g.Nx, g.Ny, 1), order='F'),
            'CO2_concentration': c[4].reshape((g.Nx, g.Ny, 1), order='F')
        }
    else:
        # c是一个1D或其他格式的数组
        c_dict = {'concentration': c.reshape((g.Nx, g.Ny, 1), order='F')}
else:
    # c已经是字典格式
    c_dict = c

# 创建孔隙度字典
if isinstance(phi, np.ndarray):
    phi_dict = {'porosity': phi.reshape((g.Nx, g.Ny, 1), order='F')}
else:
    phi_dict = phi

printf.write_outFile(g, c_dict, cfile)
printf.write_outFile(g, phi_dict, phiFile)

print("结果文件已保存:")
print(f"- 浓度场: {cfile}")
print(f"- 孔隙度场: {phiFile}")
print(f"- 监测数据: outputFiles/monitors/sulfuricAcid.xlsx")

# 4. Print final summary
print("\n=== 最终结果摘要 ===")
if len(t_arr) > 0:
    print(f"模拟时间: {t_arr[0]} - {t_arr[-1]}")
    print(f"初始矿物体积: {v_arr[0]:.6f}")
    print(f"最终矿物体积: {v_arr[-1]:.6f}")
    print(f"体积变化: {((v_arr[-1] - v_arr[0])/v_arr[0]*100):.2f}%")

    print(f"\n矿物组成变化:")
    print(f"方解石: {Calcite_arr[0]:.6f} → {Calcite_arr[-1]:.6f}")
    print(f"硅灰石: {Wollastonite_arr[0]:.6f} → {Wollastonite_arr[-1]:.6f}")
    print(f"硅酸钙: {CSH_arr[0]:.6f} → {CSH_arr[-1]:.6f}")
    print(f"石膏: {Gypsum_arr[0]:.6f} → {Gypsum_arr[-1]:.6f}")

    print(f"\n最终平均浓度:")
    print(f"H⁺: {H_arr[-1]:.2e} mol/cm³")
    print(f"Ca²⁺: {Ca_arr[-1]:.2e} mol/cm³")
    print(f"SO₄²⁻: {SO4_arr[-1]:.2e} mol/cm³")
    print(f"Si: {Si_arr[-1]:.2e} mol/cm³")
    print(f"CO₂: {CO2_arr[-1]:.2e} mol/cm³")

    if H_arr[-1] > 0:
        # 正确的pH计算：先转换为mol/L，再计算pH
        cH_molL = H_arr[-1] / unit_conversion  # 转换为 mol/L
        final_pH = -np.log10(cH_molL * gammaH_true)
        print(f"最终pH: {final_pH:.2f}")
        print(f"最终H⁺浓度: {cH_molL:.2e} mol/L")

print("=====================================")
print("硫酸矿化反应模拟完成！")
