# BioReactPy Dependencies
# Successfully tested with Python 3.10

# Core numerical computing
numpy>=1.21.0,<3.0.0
scipy>=1.7.0,<2.0.0

# Excel file operations
xlsxwriter>=3.0.0,<4.0.0
xlrd>=2.0.0,<3.0.0
openpyxl>=3.1.0,<4.0.0

# Visualization
matplotlib>=3.5.0,<4.0.0

# Optional for advanced features
# meshio>=5.0.0,<6.0.0
# mpi4py>=3.1.0,<4.0.0

# Installation notes:
# 1. Run: pip install -r requirements.txt
# 2. Create output directories before running tests
# 3. Run soulaineFlow.py first to generate flow field data
# 4. Then run sulfuricAcidMineralizationTest.py
