"""
生物矿化模型 - 耦合Stokes流动和反应传输与生物质生长

求解玄武岩溶解/微生物方解石沉淀动力学的耦合问题

该模型重现了以下论文第4.3节的结果：
Starnoni and Sanchez-Vila (2023)
Pore-scale modelling of microbially enhanced carbon mineralization

主要功能：
1. 求解多孔介质中的流体流动（Stokes方程）
2. 求解多组分反应传输方程
3. 模拟生物质生长和附着
4. 计算矿物溶解和沉淀反应
5. 更新孔隙度和渗透率

学习要点：
- 如何构建耦合的物理-化学-生物模型
- 如何处理多组分反应传输
- 如何实现Newton-Raphson化学平衡求解
- 如何更新多孔介质的物理性质
"""

import numpy as np                    # 数值计算库
import scipy.sparse as sps           # 稀疏矩阵库
from utils import fvutils             # 有限体积工具函数
from discretize import stokes, ade    # 离散化模块（Stokes方程和对流扩散方程）
import scipy.sparse.linalg           # 稀疏矩阵线性求解器
from models import flowSolver         # 流动求解器
from importExport import readf, printf # 文件读写模块
import math                          # 数学函数库

# 全局常数定义
thresh = 1e-16  # 数值精度阈值，用于质量守恒检查
eps = 1e-1      # 梯度阈值，用于确定反应表面积

def solveRT(g, d, bndf, bndt, s, h):
    """
    生物矿化反应传输主求解函数

    这是整个模型的核心函数，求解耦合的Stokes流动和反应传输问题，包含生物质生长

    模型包含8个全局传输方程：
    - 3个守恒组分方程（总碳、总氢、总氮）
    - 3个反应组分方程（硅、钙、尿素）
    - 2个生物相关方程（悬浮生物质、营养物）

    以及8个局部化学平衡方程（5个独立方程）

    参数说明：
    g: 网格对象，包含几何信息
    d: 数据字典，包含流体性质和操作条件
    bndf: 流动边界条件
    bndt: 传输边界条件
    s: 求解器参数字典
    h: 化学平衡常数字典

    返回值：
    p: 压力场解
    u: 速度场解
    species: 化学组分浓度字典
    volumefractions: 体积分数字典（孔隙度、矿物等）
    monitors: 监测数据字典（时间序列数据）

    学习要点：
    1. 如何组织复杂的多物理场耦合问题
    2. 如何处理多组分反应传输
    3. 如何实现时间步进算法
    4. 如何管理和更新物理性质
    """

    # ==================== 第1步：读取输入参数 ====================
    # 这一部分从字典中提取所有需要的物理、化学和生物参数

    # 流体性质参数
    rho = d["fluid density"]                    # 流体密度 [g/cm³]
    mu = d["fluid viscosity"]                   # 流体粘度 [g/(cm·s)]
    initial_permeability = d["initial permeability"]  # 初始渗透率 [cm²]
    diff = d["diffusion coefficient"]           # 扩散系数 [cm²/s]

    # 矿物性质参数
    VmCalcite = d["molar volume calcite"]       # 方解石摩尔体积 [cm³/mol]
    VmWollastonite = d["molar volume wollastonite"]  # 硅灰石摩尔体积 [cm³/mol]

    # 反应动力学参数
    _kappaDiss = d["dissolution rate constant"]  # 溶解速率常数 [mol/(cm²·s)]
    _kappaPrec = d["precipitation rate constant"] # 沉淀速率常数 [mol/(cm²·s)]
    startDissolutionTime = d["start Dissolution Time"]  # 溶解开始时间 [s]

    # 生物过程参数
    kAttach = d["Biomass attachment rate"]      # 生物质附着速率 [1/s]
    kDec1 = d["Endogenous decay rate"]          # 内源衰减速率 [1/s]
    kDec2 = d["Decay rate due to calcite precipitation"]  # 因沉淀导致的衰减速率
    KU = d["Half saturation constant of urea"]  # 尿素半饱和常数 [mol/g]
    KN = d["Half saturation constant of nutrient"]  # 营养物半饱和常数 [mol/cm³]
    kappaMu = d["Maximum substrate utilization rate"]  # 最大基质利用速率 [1/s]
    Y = d["Yield coefficient"]                  # 产率系数 [无量纲]
    MU = d["molar mass urea"]                   # 尿素摩尔质量 [g/mol]
    kappaUreo = d["Urea growth rate"]           # 尿素生长速率 [mol/(g·s)]
    rhoB = d["density of biofilm"]              # 生物膜密度 [g/cm³]

    # 文件路径和更新准则
    flowfile = d["flow filename"]               # 流场文件路径
    fluxfile = d["flux filename"]               # 通量文件路径
    updateFlowCriterion = d["update flow criterion"]  # 流场更新准则

    # 时间和求解参数
    dt = s["time step"]                         # 时间步长 [s]
    t_end = d["end time"]                       # 结束时间 [s]
    tolNR = s["tolerance speciation"]           # Newton-Raphson收敛容差

    # ==================== 第2步：化学平衡参数 ====================
    # 活度系数 - 用于计算化学活度，考虑离子强度效应
    gammaH = h["activity coefficient H"]        # H⁺活度系数
    gammaCO3 = h["activity coefficient CO3"]    # CO₃²⁻活度系数
    gammaCa = h["activity coefficient Ca"]      # Ca²⁺活度系数
    gammaSiO2 = h["activity coefficient SiO2"]  # SiO₂活度系数
    gammaCO2 = h["activity coefficient CO2"]    # CO₂活度系数
    gammaUrea = h["activity coefficient Urea"]  # 尿素活度系数
    gammaNH3 = h["activity coefficient NH3"]    # NH₃活度系数
    gammaNH4 = h["activity coefficient NH4"]    # NH₄⁺活度系数

    # 将活度系数组织成数组，便于传递给化学平衡求解函数
    # 顺序：CO3, H, CO2, Ca, SiO2, Urea, NH3, NH4
    gamma = np.array(
        [gammaCO3, gammaH, gammaCO2, gammaCa, gammaSiO2, gammaUrea, gammaNH3, gammaNH4]
        )

    # 平衡常数（对数形式）
    logKeqH2CO3 = h["equilibrium constant carbonate"]  # 碳酸平衡常数
    logKeqCaCo3 = h["equilibrium constant calcite"]    # 方解石溶解度积常数
    logKeqNH4 = h["equilibrium constant nitrogen"]     # 氨/铵平衡常数

    # 方解石溶解度积（线性形式）
    Ksp = math.pow(10, logKeqCaCo3)

    # ==================== 第3步：边界条件设置 ====================
    # 从数据字典中读取各种边界条件
    u_bound = d["boundary velocity"]            # 速度边界条件
    p_bound = d["boundary pressure"]            # 压力边界条件
    c1_bnd = d["boundary concentration first component"]   # 第1组分边界浓度
    c2_bnd = d["boundary concentration second component"]  # 第2组分边界浓度
    c3_bnd = d["boundary concentration third component"]   # 第3组分边界浓度
    c4_bnd = d["boundary concentration fourth component"]  # 第4组分边界浓度
    c5_bnd = d["boundary concentration fifth component"]   # 第5组分边界浓度
    c6_bnd = d["boundary concentration sixth component"]   # 第6组分边界浓度
    cB_bnd = d["boundary concentration biomass"]           # 生物质边界浓度
    cN_bnd = d["boundary concentration nutrient"]          # 营养物边界浓度

    # ==================== 第4步：变量初始化 ====================
    # 从文件读取初始流场（压力和速度）
    p, u = readf.read_flowfield(g, flowfile)

    # 初始化学组分浓度 [mol/cm³]
    cH = d["initial concentration H"]           # H⁺浓度
    cCO3 = d["initial concentration CO3"]       # CO₃²⁻浓度
    cCO2 = d["initial concentration CO2"]       # CO₂浓度
    cCa = d["initial concentration Ca"]         # Ca²⁺浓度
    cSiO2 = d["initial concentration Si"]       # SiO₂浓度
    cUrea = d["initial concentration Urea"]     # 尿素浓度
    cNH3 = d["initial concentration NH3"]       # NH₃浓度
    cNH4 = d["initial concentration NH4"]       # NH₄⁺浓度
    cB = d["initial biomass concentration"]     # 悬浮生物质浓度
    cN = d["initial nutrient concentration"]    # 营养物浓度

    # ==================== 第5步：守恒组分计算 ====================
    # 将化学组分转换为守恒组分，这样可以减少传输方程的数量
    # 守恒组分在没有反应的情况下保持守恒
    c1 = cCO2 + cCO3 - cCa + cSiO2 + cUrea     # 总碳平衡（考虑电荷平衡）
    c2 = cH + 2*cCO2 + 2*cSiO2 + 2*cUrea + cNH4  # 总氢平衡
    c3 = cNH3 + cNH4 + 2*cUrea                 # 总氮平衡
    c4 = cSiO2                                 # 硅组分（反应性）
    c5 = cCa                                   # 钙组分（反应性）
    c6 = cUrea                                 # 尿素组分（反应性）
    
    phi = d["initial porosity field"]
    phi_m = d["initial mineral field"]
    phi_b = d["initial biofilm"]

    phi_ws = phi_m.copy()
    phi_cc = np.zeros(g.num_cells)
     
    # Initialize advection matrices
    q = readf.read_fluxfield(g, fluxfile)

    U1, U1_bnd = ade.discretize_advection(g, bndt, q, c1_bnd)
    U2, U2_bnd = ade.discretize_advection(g, bndt, q, c2_bnd)
    U3, U3_bnd = ade.discretize_advection(g, bndt, q, c3_bnd)
    U4, U4_bnd = ade.discretize_advection(g, bndt, q, c4_bnd)
    U5, U5_bnd = ade.discretize_advection(g, bndt, q, c5_bnd)
    U6, U6_bnd = ade.discretize_advection(g, bndt, q, c6_bnd)
    Ub, Ub_bnd = ade.discretize_advection(g, bndt, q, cB_bnd)
    Un, Un_bnd = ade.discretize_advection(g, bndt, q, cN_bnd)
    
    # Initialize monitors        
    l_time = []
    l_surf = []
    l_vol = []
    l_bio = []

    l_H = []
    l_Ca = []
    l_CO3 = []
    l_SiO2 = []
    l_CO2 = []
    l_Urea = []
    l_NH3 = []
    l_NH4 = []
    l_CaSiO3 = []
    l_CaCO3 = []

    t = 0
    mineralVolume_old = 0

    MineralVolumeOld = np.sum(phi_m * g.cell_volumes)

    SIprec = np.zeros(g.num_cells)
    kappaPrec = np.zeros(g.num_cells)

    # Precipitation is only activated when the local SI > 1
    # Until then, calcium is treated in the same fashion as silica
    precipitation = False

    SIp = np.zeros(g.num_cells)

    spCells = []

    # ==================== 第7步：主时间循环 ====================
    # 这是模型的核心部分，在每个时间步中求解所有耦合方程
    while t <= t_end:

        # ========== 步骤0：保存上一时间步的值 ==========
        # 保存所有组分的旧值，用于时间离散化
        c1_old = c1.copy()      # 第1守恒组分
        c2_old = c2.copy()      # 第2守恒组分
        c3_old = c3.copy()      # 第3守恒组分
        c4_old = c4.copy()      # 硅组分
        c5_old = c5.copy()      # 钙组分
        c6_old = c6.copy()      # 尿素组分
        cB_old = cB.copy()      # 悬浮生物质
        cN_old = cN.copy()      # 营养物

        spCellsOld = spCells.copy()  # 保存反应活跃单元列表

        # ========== 步骤1：更新有效性质 ==========
        # 计算孔隙度梯度，用于确定反应表面积
        sigma = fvutils.gradient_of_scalar(g, phi)
        # 去除梯度过小的单元（避免数值噪声）
        threshCells = np.ravel(np.argwhere(sigma < eps))
        sigma[threshCells] = 0

        # 计算各种体积量（用于监测和质量守恒检查）
        mineralArea = np.sum(sigma * g.cell_volumes)      # 矿物表面积
        mineralVolume = np.sum(phi_m * g.cell_volumes)    # 总矿物体积
        biofilmVolume = np.sum(phi_b * g.cell_volumes)    # 生物膜体积

        Wollastonite = np.sum(phi_ws * g.cell_volumes)    # 硅灰石体积
        Calcite = np.sum(phi_cc * g.cell_volumes)         # 方解石体积

        # 质量守恒检查：硅灰石+方解石应该等于总矿物体积
        eps_vol = abs(Wollastonite + Calcite - mineralVolume)
        assert eps_vol < thresh, f"质量守恒违反: {eps_vol}"

        # 计算矿物体积变化，用于判断是否需要更新流场
        MineralVolumeChange = abs(mineralVolume-MineralVolumeOld)
        relMineralVolumeChange = MineralVolumeChange / MineralVolumeOld
        
        # ========== 步骤5：根据需要更新流场 ==========
        # 当矿物体积变化超过阈值时，重新计算流场
        if relMineralVolumeChange > updateFlowCriterion:
            print(
                '-------------------------------------------------- Update flow --------------------------------------------------')
            d["porosity field"] = phi
            # 注意：这里应该使用flowSolver而不是Stokes，并且bnd应该是bndf
            p, u, q = flowSolver.StokesSolver(g, d, bndf, s, p, u)

            U1, U1_bnd = ade.discretize_advection(g, bndt, q, c1_bnd)
            U2, U2_bnd = ade.discretize_advection(g, bndt, q, c2_bnd)
            U3, U3_bnd = ade.discretize_advection(g, bndt, q, c3_bnd)
            U4, U4_bnd = ade.discretize_advection(g, bndt, q, c4_bnd)
            U5, U5_bnd = ade.discretize_advection(g, bndt, q, c5_bnd)
            U6, U6_bnd = ade.discretize_advection(g, bndt, q, c6_bnd)
            Ub, Ub_bnd = ade.discretize_advection(g, bndt, q, cB_bnd)
            Un, Un_bnd = ade.discretize_advection(g, bndt, q, cN_bnd)

            MineralVolumeOld = mineralVolume

        psi = 4 * phi * phi_m

        A1, A1_bnd = ade.discretize_diffusion(g, bndt, diff*phi, c1_bnd)
        A2, A2_bnd = ade.discretize_diffusion(g, bndt, diff*phi, c2_bnd)
        A3, A3_bnd = ade.discretize_diffusion(g, bndt, diff*phi, c3_bnd)
        A4, A4_bnd = ade.discretize_diffusion(g, bndt, diff*phi, c4_bnd)
        A5, A5_bnd = ade.discretize_diffusion(g, bndt, diff*phi, c5_bnd)
        A6, A6_bnd = ade.discretize_diffusion(g, bndt, diff*phi, c6_bnd)
        Ab, Ab_bnd = ade.discretize_diffusion(g, bndt, diff*phi, cB_bnd)
        An, An_bnd = ade.discretize_diffusion(g, bndt, diff*phi, cN_bnd)
        
        # Step 2 - Solve transport
        M = sps.diags(phi * g.cell_volumes / dt)

        # Component 1 - conservative
        a = M + U1 + A1
        b = U1_bnd + A1_bnd + phi * g.cell_volumes / dt * c1_old
        c1 = scipy.sparse.linalg.spsolve(a, b)

        # Component 2 - conservative
        a = M + U2 + A2
        b = U2_bnd + A2_bnd + phi * g.cell_volumes / dt * c2_old
        c2 = scipy.sparse.linalg.spsolve(a, b)

        # Component 3 - conservative
        a = M + U3 + A3
        b = U3_bnd + A3_bnd + phi * g.cell_volumes / dt * c3_old
        c3 = scipy.sparse.linalg.spsolve(a, b)
        
        # Component 4 - Silica (kinetic reactive)
        # dissolution only occurs on cells containing the mineral
        if t < startDissolutionTime:
            kappaDiss = 0
        else:
            kappaDiss = _kappaDiss
        a = M + U4 + A4
        R = kappaDiss * sigma * psi * g.cell_volumes
        b = U4_bnd + A4_bnd + phi * g.cell_volumes / dt * c4_old + R
        c4 = scipy.sparse.linalg.spsolve(a, b)

        # Component 5 - Calcium (kinetic reactive)
        # preciptation can occur everywhere near the mineral
        if precipitation:
            Rdiss = kappaDiss * sigma * psi
            Rprec = kappaPrec * sigma
            R = (Rdiss - Rprec) * g.cell_volumes
            b = U5_bnd + A5_bnd + phi * g.cell_volumes / dt * c5_old + R
            c5 = scipy.sparse.linalg.spsolve(a, b)
        else:
            c5 = c4

        # Component 6 - Urea (kinetic reactive)

        # First calculate molality from molar concentration
        # c is in mol/cm3, rho is in g/cm3, MU is in g/mol
        # as a result, molU is in mol/g
        molU = np.divide(c6_old, rho - c6_old * MU)
        # then calculate the Michaelis‐Menten kinetic term
        term = np.divide(molU, KU + molU)
        # finally, calculate the rate of ureolysis [mol/cm3 s]
        R = kappaUreo * rhoB * phi_b * term
        # assemble right hand side
        b = U6_bnd + A6_bnd + (phi / dt * c6_old - R) * g.cell_volumes
        c6 = scipy.sparse.linalg.spsolve(a, b)
           
        # Do speciation only in relevant cells,
        # where actual dissolution/precipitation is used
        # to calculate the porosity change
        # This is to save computational effort when diss./prec. is
        # localized in cells around the mineral grains. The model
        # as it is can already handle speciation in all cells,
        # however at a not negligible computational cost, and also
        # needs very small time steps in the initial calculations to
        # maintain the Newton-Raphson solution stable.
        spCells = np.ravel(np.argwhere(sigma > 0))
        checkSpCells = np.array_equal(spCells, spCellsOld)
        if not checkSpCells:
            print('Warning: new SpCells')
            mask = np.isin(spCells, spCellsOld, invert=True)
            newSpCells = spCells[mask]            
            print(newSpCells)
            print(sigma[newSpCells])

        for j in spCells:

            x = g.cell_centers[0, j]

            check = False

            if check:
                print('cell', j, 'sigma', sigma[j], 'phi', phi[j])

            cSiO2[j] = c4[j]
            cCa[j] = c5[j]
            cUrea[j] = c6[j]

            x1 = cCO3[j] 
            x2 = cH[j]
            x3 = cCO2[j]
            x4 = cNH3[j]
            x5 = cNH4[j]

            x0 = np.array([x1, x2, x3, x4, x5])
               
            b = np.array(
                [c1[j], c2[j], c3[j], logKeqH2CO3, logKeqNH4, c4[j], c5[j], c6[j]]
                )
            x, flag = newton_raphson(
                x0, b, gamma, tol = tolNR, maxiter = 10, check = False
                )

            if flag != 0:
                print(
                    '-------------- WARNING failed speciation in cell', j, flag
                    )
                # 如果第一次Newton-Raphson失败，尝试更详细的调试模式
                x, flag = newton_raphson(
                    x0, b, gamma, tol = tolNR, maxiter = 10, check=True
                    )
                assert False

            assert np.all(x > 0)
                
            cCO3[j] = x[0]
            cH[j] = x[1]
            cCO2[j] = x[2]
            cNH3[j] = x[3]
            cNH4[j] = x[4]

        # Calculate rate of precipitation
        # from the chemical activities
        SIp = gammaCa*cCa * gammaCO3*cCO3 / Ksp
        maxSIp = np.amax(SIp[spCells])            

        # Check if conditions for precipitation are met
        if ((maxSIp > 1) and (not precipitation)):
            precipitation = True

        maxSIpInd = np.argmax(SIp[spCells])
        #print('time', t, 'max precipitation SI', maxSIp, 'in cell', maxSIpInd)

        # Calculate dissolution / precipitation rates [units of 1/s]
        kappaPrec =  np.maximum(0, - _kappaPrec * (1 - SIp))
        rPrec = sigma * kappaPrec * VmCalcite
        rDiss = sigma * psi * kappaDiss * VmWollastonite

        # Calculate and store monitor quantities    
        if t % 1 == 0:

            print('time', t)
           
            l_time.append(int(t))
            l_surf.append(mineralArea)
            l_vol.append(mineralVolume)
            l_bio.append(biofilmVolume)

            # Calculate locally average species concentrations
            phiV = phi * g.cell_volumes
            den = np.sum(phiV[spCells])

            avgH = np.sum(cH[spCells] * phiV[spCells]) / den
            avgCO3 = np.sum(cCO3[spCells] * phiV[spCells]) / den
            avgCO2 = np.sum(cCO2[spCells] * phiV[spCells]) / den
            avgCa = np.sum(cCa[spCells] * phiV[spCells]) / den
            avgSiO2 = np.sum(cSiO2[spCells] * phiV[spCells]) / den

            avgNH3 = np.sum(cNH3[spCells] * phiV[spCells]) / den
            avgNH4 = np.sum(cNH4[spCells] * phiV[spCells]) / den
            avgUrea = np.sum(cUrea[spCells] * phiV[spCells]) / den          

            l_H.append(avgH)
            l_CO3.append(avgCO3)
            l_CO2.append(avgCO2)
            l_Ca.append(avgCa)
            l_SiO2.append(avgSiO2)

            l_NH3.append(avgNH3)
            l_NH4.append(avgNH4)
            l_Urea.append(avgUrea)                      

            l_CaSiO3.append(Wollastonite)
            l_CaCO3.append(Calcite)

            print(
                'max precipitation index', maxSIp, 'in cell', maxSIpInd
                )
            print(
                'Average H', avgH, 'Average CO3', avgCO3
                )
            print(
                'Average CO2', avgCO2, 'Average Urea', avgUrea
                )
            print(
                'Average Ca', avgCa, 'Average SiO2', avgSiO2
                )
            print(
                'Average NH3', avgNH3, 'Average NH4', avgNH4
                ) 
            
            print('Total Mineral volume', mineralVolume)
            print('Wollastonite volume', Wollastonite)
            print('Calcite volume', Calcite)
            print('biofilm volume', biofilmVolume)

        # Component 7 - Suspended biomass
        # attachement occurs in proximity of the mineral (sigma != 0)
        muN = kappaMu * np.divide(cN, KN + cN)
        Rgrowth = muN * phi
        Rattach = kAttach * sigma
        R = sps.diags((Rgrowth - Rattach) * g.cell_volumes)
        a = M + Ub + Ab - R
        b = Ub_bnd + Ab_bnd + phi * g.cell_volumes / dt * cB_old
        cB = scipy.sparse.linalg.spsolve(a, b)

        # Update biofilm
        Rgrowth = muN * phi_b * rhoB
        Rattach = kAttach * sigma * cB
        Rdecay = (kDec1 + kDec2 * rPrec) * phi_b * rhoB
        
        phi_b += dt * (Rgrowth + Rattach - Rdecay) / rhoB
      
        # Step 4 - Update porosity
        phi_ws -= dt * rDiss
        phi_cc += dt * rPrec

        phi_m = phi_ws + phi_cc

        phi = 1 - phi_m - phi_b
       
        assert np.all(phi >= 0)
        assert np.all(phi <= 1)

        assert np.all(phi_m >= 0)
        assert np.all(phi_m <= 1)

        assert np.all(phi_m >= 0)
        assert np.all(phi_m <= 1)

        # Component 8 - Nutrient
        Rn = - muN / Y * (phi_b * rhoB + phi * cB) * g.cell_volumes
        a = M + Un + An
        b = Un_bnd + An_bnd + phi * g.cell_volumes / dt * cN_old + Rn
        cN = scipy.sparse.linalg.spsolve(a, b)

        """
        # is speciation is done in all cells, we need to progressively
        # adjust the time step, since very small time steps are needed
        # in the initial calculations to maintain Newton-Raphson stable
        if t == 0.02:
            dt = 0.0001
        if t == 0.1:
            dt = 0.001
        if t == 1:
            dt = 0.0025
        """
        
        # Step 6 - Proceed to next time step
        t = round(t+dt, 6)

    # Save return quantities in relevant dictionaries  
    monitors = {
        "time": l_time,
        "mineral surface": l_surf,
        "mineral volume": l_vol,
        "sum H": l_H,
        "sum Ca": l_Ca,
        "sum CO3": l_CO3,
        "sum SiO2": l_SiO2,
        "sum CO2": l_CO2,
        "sum NH3": l_NH3,
        "sum NH4": l_NH4,
        "sum urea": l_Urea,        
        "wollastonite volume": l_CaSiO3,
        "calcite volume": l_CaCO3,
        "biofilm": l_bio,
        }

    species = {
        "H": cH.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "Ca": cCa.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "CO3": cCO3.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "SiO2": cSiO2.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "CO2": cCO2.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "NH3": cNH3.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "NH4": cNH4.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "Urea": cUrea.reshape((g.Nx, g.Ny, 1), order = 'F'),
        }

    volumefractions = {
        "phi": phi.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "phi_cc": phi_cc.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "sigma": sigma.reshape((g.Nx, g.Ny, 1), order = 'F'),
        "sigmaPsi": (sigma * psi).reshape((g.Nx, g.Ny, 1), order = 'F'),
        "SIp": SIp.reshape((g.Nx, g.Ny, 1), order = 'F'),
        }    

    return p, u, species, volumefractions, monitors

def jacobian(x, g, b):
    """
    计算化学平衡方程组的雅可比矩阵

    这个函数是Newton-Raphson方法的核心，用于求解非线性化学平衡方程组

    参数说明：
    x: 当前迭代值 [CO3, H, CO2, NH3, NH4] (5个未知数)
    g: 活度系数数组
    b: 右端项（守恒组分和平衡常数）

    返回值：
    jac: 5×5雅可比矩阵，包含所有偏导数

    学习要点：
    1. 雅可比矩阵是Newton-Raphson方法的关键
    2. 每一行对应一个方程，每一列对应一个变量的偏导数
    3. 对数形式的平衡方程需要特殊处理偏导数
    """

    ln10 = math.log(10)  # 自然对数转换为常用对数的因子

    # 提取相关的活度系数
    g0 = g[0]  # CO3活度系数
    g1 = g[1]  # H活度系数
    g2 = g[2]  # CO2活度系数
    g3 = g[6]  # NH3活度系数
    g4 = g[7]  # NH4活度系数

    # 构建雅可比矩阵的每一行
    # 第1行：总碳平衡方程的偏导数 ∂f1/∂x
    r1 = np.array([1, 0, 1, 0, 0])  # [∂f1/∂CO3, ∂f1/∂H, ∂f1/∂CO2, ∂f1/∂NH3, ∂f1/∂NH4]

    # 第2行：总氢平衡方程的偏导数 ∂f2/∂x
    r2 = np.array([0, 1, 2, 0, 1])  # [∂f2/∂CO3, ∂f2/∂H, ∂f2/∂CO2, ∂f2/∂NH3, ∂f2/∂NH4]

    # 第3行：总氮平衡方程的偏导数 ∂f3/∂x
    r3 = np.array([0, 0, 0, 1, 1])  # [∂f3/∂CO3, ∂f3/∂H, ∂f3/∂CO2, ∂f3/∂NH3, ∂f3/∂NH4]

    # 第4行：碳酸平衡方程的偏导数（对数形式）∂f4/∂x
    r4 = np.array([1/(x[0]*ln10), 2/(x[1]*ln10), -1/(x[2]*ln10), 0, 0])

    # 第5行：氨/铵平衡方程的偏导数（对数形式）∂f5/∂x
    r5 = np.array([0, 1/(x[1]*ln10), 0, 1/(x[3]*ln10), -1/(x[4]*ln10)])

    # 组装完整的雅可比矩阵
    jac = np.vstack((r1, r2, r3, r4, r5))

    return jac

def function(x, b, g):
    """
    计算化学平衡方程组的残差函数值

    这个函数定义了需要求解的5个非线性方程，代表局部化学平衡条件

    方程组说明：
    1. f1: 总碳平衡方程 (CO3 + CO2 - Ca + SiO2 + Urea = c1)
    2. f2: 总氢平衡方程 (H + 2*CO2 + 2*SiO2 + 2*Urea + NH4 = c2)
    3. f3: 总氮平衡方程 (NH3 + NH4 + 2*Urea = c3)
    4. f4: 碳酸平衡方程 (log10(γCO3*CO3) + 2*log10(γH*H) - log10(γCO2*CO2) = logKeq)
    5. f5: 氨/铵平衡方程 (log10(γH*H) + log10(γNH3*NH3) - log10(γNH4*NH4) = logKeq)

    参数说明：
    x: 当前迭代值 [CO3, H, CO2, NH3, NH4] (5个未知数)
    b: 右端项数组，包含：
       b[0-2]: 守恒组分 c1, c2, c3
       b[3-4]: 平衡常数 logKeqH2CO3, logKeqNH4
       b[5-7]: 反应组分 SiO2, Ca, Urea
    g: 活度系数数组

    返回值：
    func: 5个方程的残差值，当解收敛时应该接近零

    学习要点：
    1. 化学平衡问题通常包含质量守恒和化学平衡两类约束
    2. 对数形式的平衡方程更数值稳定
    3. 活度系数用于考虑离子强度效应
    4. 残差函数的设计直接影响Newton-Raphson的收敛性
    """

    # 变量顺序：CO3, H, CO2, NH3, NH4

    # 从右端项中提取反应组分浓度
    SiO2 = b[5]  # 硅酸浓度
    Ca = b[6]    # 钙离子浓度
    Urea = b[7]  # 尿素浓度

    # 提取相关活度系数
    g0 = g[0]  # γCO3
    g1 = g[1]  # γH
    g2 = g[2]  # γCO2
    g3 = g[6]  # γNH3
    g4 = g[7]  # γNH4

    def log10(a):
        return math.log10(a)

    # 方程1：总碳平衡 (考虑电荷平衡)
    f1 = x[0] + x[2] - Ca + SiO2 + Urea - b[0]

    # 方程2：总氢平衡
    f2 = x[1] + 2*x[2] + 2*SiO2 + 2*Urea + x[4] - b[1]

    # 方程3：总氮平衡
    f3 = x[3] + x[4] + 2*Urea - b[2]

    # 方程4：碳酸平衡 H2CO3 ⇌ H+ + HCO3- ⇌ 2H+ + CO3²-
    f4 = log10(g0*x[0]) + 2*log10(g1*x[1]) - log10(g2*x[2]) - b[3]

    # 方程5：氨/铵平衡 NH3 + H+ ⇌ NH4+
    f5 = log10(g1*x[1]) + log10(g3*x[3]) - log10(g4*x[4]) - b[4]

    # 组装残差向量
    func = np.array([f1, f2, f3, f4, f5])

    return func

def newton_iteration(x, b, g):

    """
    Perform a Newton-Raphson iteration

    Inputs:
    x: the current values
    b: the right hand side
    g: the activity coefficients
   
    Returns:
    x_new: the updated values, dimensions: number of variables
    """
    
    j = jacobian(x, g, b)

    f = function(x, b, g)

    y = np.linalg.solve(j, -f)

    x_new = x + y
   
    return x_new

def newton_raphson(x_init, b, g, tol, maxiter, check=False):
    """
    使用Newton-Raphson方法求解局部化学平衡方程组

    这是整个化学平衡求解的主控函数，在每个网格单元中被调用

    算法流程：
    1. 从初始猜测开始
    2. 迭代计算残差和雅可比矩阵
    3. 求解线性修正方程
    4. 更新解并检查收敛
    5. 重复直到收敛或达到最大迭代次数

    参数说明：
    x_init: 初始猜测 [CO3, H, CO2, NH3, NH4]
    b: 右端项，包含守恒组分和平衡常数
    g: 活度系数数组
    tol: 收敛容差（通常1e-12）
    maxiter: 最大迭代次数（通常50）
    check: 调试标志，打印详细信息

    返回值：
    x_new: 收敛解
    flag: 收敛状态（1=成功，0或负数=失败）

    学习要点：
    1. 化学平衡求解是反应传输模型的核心
    2. 需要良好的初始猜测来保证收敛
    3. 容差设置影响精度和计算效率
    4. 失败的情况需要特殊处理
    """
    
    counter = 0
    flag = 0

    x_old = x_init.copy()

    if check:
        print('b', b)
        print(counter, x_old)

    initial_res = 0

    while counter < maxiter:

        counter += 1

        x_new = newton_iteration(x_old, b, g)
        if check:
            print(counter, x_new)

        res = np.linalg.norm(x_old-x_new) / np.linalg.norm(x_new)
        if check:
            print('residual', res)
        if res < tol:
            break

        x_old = x_new

    if counter > maxiter - 2:
        flag = - counter

    return x_new, flag
