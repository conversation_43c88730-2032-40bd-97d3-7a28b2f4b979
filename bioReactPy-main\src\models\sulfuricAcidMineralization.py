"""
Solves coupled Stokes flow and reactive transport
for sulfuric acid dissolution and precipitation kinetics

This model simulates the following chemical reactions:
1. H₂SO₄ → 2H⁺ + SO₄²⁻                    (complete ionization)
2. CaCO₃ + 2H⁺ → Ca²⁺ + CO₂ + H₂O         (calcite dissolution)
3. CaSiO₃ + 2H⁺ → Ca²⁺ + H₂SiO₃           (wollastonite dissolution)
4. Ca²⁺ + H₄SiO₄ → CaSiO₃ + 2H₂O + 2H⁺    (calcium silicate precipitation)
5. Ca²⁺ + SO₄²⁻ + 2H₂O → CaSO₄·2H₂O       (gypsum precipitation)

We have 5 global transport equations and 5 local chemical equations
"""

import numpy as np
import scipy.sparse as sps
from utils import fvutils
from discretize import stokes, ade
import scipy.sparse.linalg
from models import flowSolver
from importExport import readf, printf
import math

thresh = 1e-16
eps = 1e-4
                
def solveRT(g, d, bndf, bndt, s, h):

    """
    Solves coupled Stokes flow and reactive transport
    for sulfuric acid dissolution and precipitation kinetics

    We have 5 global transport equations:
    - H⁺ concentration
    - Ca²⁺ concentration  
    - SO₄²⁻ concentration
    - Si concentration
    - CO₂ concentration

    Inputs:
    g: the grid
    d: data dictionary for fluid properties and operating conditions
    bndf: boundary conditions for flow
    bndt: boundary conditions for transport
    s: data dictionary for the flow and linear solvers
    h: data dictionary for chemical data
    
    Returns:
    p: solution of pressure
    u: solution of velocity
    c: solution of concentration
    phi: final porosity field
    l: dictionary with monitoring data
    """

    # Unpack flow data
    rho_f = d["fluid density"]
    mu = d["fluid viscosity"]
    u_bound = d["boundary velocity"]
    p_bound = d["boundary pressure"]
    
    # Unpack boundary conditions for transport
    c1_bound = d["boundary concentration first component"]   # H⁺
    c2_bound = d["boundary concentration second component"]  # Ca²⁺
    c3_bound = d["boundary concentration third component"]   # SO₄²⁻
    c4_bound = d["boundary concentration fourth component"]  # Si
    c5_bound = d["boundary concentration fifth component"]   # CO₂
    
    # Unpack initial conditions
    cH_in = d["initial concentration H"]
    cCa_in = d["initial concentration Ca"]
    cSO4_in = d["initial concentration SO4"]
    cSi_in = d["initial concentration Si"]
    cCO2_in = d["initial concentration CO2"]
    
    phi = d["initial porosity field"]
    phi_m = d["initial mineral field"]
    k = d["initial permeability"]
    
    # Unpack mineral properties
    VmCalcite = d["molar volume calcite"]
    VmWollastonite = d["molar volume wollastonite"]
    VmGypsum = d["molar volume gypsum"]
    
    # Unpack diffusion coefficient
    diff = d["diffusion coefficient"]
    
    # Unpack kinetic parameters
    kappaDiss_calcite = d["calcite dissolution rate constant"]
    kappaDiss_wollastonite = d["wollastonite dissolution rate constant"]
    kappaPrec_CSH = d["CSH precipitation rate constant"]
    kappaPrec_gypsum = d["gypsum precipitation rate constant"]
    
    # Unpack time parameters
    t_end = d["end time"]
    dt = s["time step"]
    startDissolutionTime = d["start Dissolution Time"]
    UpdateFlow = d["update flow criterion"]
    
    # Unpack chemical data
    gammaH = h["activity coefficient H"]
    gammaCa = h["activity coefficient Ca"]
    gammaSO4 = h["activity coefficient SO4"]
    gammaSiO2 = h["activity coefficient SiO2"]
    gammaCO2 = h["activity coefficient CO2"]
    
    # Equilibrium constants
    logKeqCaCO3 = h["equilibrium constant calcite"]
    logKeqH4SiO4_1 = h["equilibrium constant H4SiO4_1"]
    logKeqGypsum = h["equilibrium constant gypsum"]
    
    Ksp_calcite = math.pow(10, logKeqCaCO3)
    Ksp_gypsum = math.pow(10, logKeqGypsum)
    
    print('Ksp calcite', Ksp_calcite)
    print('Ksp gypsum', Ksp_gypsum)
    
    # Unpack solver data
    FlowModel = s["Flow model"]
    omega_u = s["omega u"]
    omega_p = s["omega p"]
    iter_u = s["inner iterations u"]
    tol_u = s["inner tolerance u"]
    tol_p = s["inner tolerance p"]
    tol_continuity = s["tolerance continuity"]
    convergence_criterion = s["convergence criterion"]
    tol_discharge = s["tolerance discharge"]
    outer_iterations = s["max outer iterations"]
    tolNR = s["tolerance speciation"]
    
    # Read flow field
    flowfile = d["flow filename"]
    fluxfile = d["flux filename"]
    
    # Initialize components from initial conditions
    # Component definitions based on mass conservation:
    # c1 = H⁺ concentration
    # c2 = Ca²⁺ concentration  
    # c3 = SO₄²⁻ concentration
    # c4 = Si concentration
    # c5 = CO₂ concentration
    
    c1 = cH_in.copy()
    c2 = cCa_in.copy()
    c3 = cSO4_in.copy()
    c4 = cSi_in.copy()
    c5 = cCO2_in.copy()
    
    # Initialize mineral volume fractions
    phi_calcite = np.zeros(g.num_cells)
    phi_wollastonite = phi_m.copy()  # Initially all mineral is wollastonite
    phi_CSH = np.zeros(g.num_cells)  # No initial CSH
    phi_gypsum = np.zeros(g.num_cells)  # No initial gypsum
    
    phi_total_solid = phi_calcite + phi_wollastonite + phi_CSH + phi_gypsum
    phi = 1 - phi_total_solid
          
    # Initialize flow field
    print("Initializing flow field")

    # Try to read flux field first
    try:
        q = readf.read_fluxfield(g, fluxfile)
        print("Flux field read successfully")

        # Initialize pressure and velocity fields
        p = np.zeros(g.num_cells)
        u = np.zeros((g.dim, g.num_faces))

        print("Flow field initialized from file")

    except Exception as e:
        print(f"Warning: Could not read flux field: {e}")
        print("Attempting to solve flow field...")

        # Initialize with zero fields
        p = np.zeros(g.num_cells)
        u = np.zeros((g.dim, g.num_faces))

        try:
            # Solve initial flow
            d["porosity field"] = phi
            p, u, q = flowSolver.SimpleAlgorithm(g, d, bndf, s, p, u)
            print("Initial flow field solved")
        except Exception as flow_error:
            print(f"Flow solver failed: {flow_error}")
            print("Using simplified uniform flow field")

            # Create a simple uniform flow field
            q = np.ones(g.num_faces) * 0.1  # Simple uniform flux
            print("Simplified flow field created")

    # Initialize advection matrices
    U1, U1_bound = ade.discretize_advection(g, bndt, q, c1_bound)
    U2, U2_bound = ade.discretize_advection(g, bndt, q, c2_bound)
    U3, U3_bound = ade.discretize_advection(g, bndt, q, c3_bound)
    U4, U4_bound = ade.discretize_advection(g, bndt, q, c4_bound)
    U5, U5_bound = ade.discretize_advection(g, bndt, q, c5_bound)
   
    # Initialize monitors        
    l_time = []
    l_surf = []
    l_vol = []
    l_H = []
    l_Ca = []
    l_SO4 = []
    l_Si = []
    l_CO2 = []
    l_calcite = []
    l_wollastonite = []
    l_CSH = []
    l_gypsum = []

    t = 0
    MineralVolumeOld = np.sum(phi_total_solid * g.cell_volumes)
    
    # Flags for precipitation
    precipitation_CSH = False
    precipitation_gypsum = False
    
    print("=== 硫酸矿化反应模拟开始 ===")
    print(f"初始矿物体积: {MineralVolumeOld}")
    print(f"模拟时间: {t_end}")
    print(f"时间步长: {dt}")
    print("=====================================")

    iteration_count = 0
    max_iterations = int(t_end / dt) + 1

    while t <= t_end:
        iteration_count += 1

        # 显示进度
        if iteration_count % 100 == 0:
            progress = (t / t_end) * 100
            print(f"Progress: {progress:.1f}% (t={t:.3f}/{t_end})")

        # 早期退出条件 (如果数值不稳定)
        if iteration_count > max_iterations:
            print(f"Warning: Maximum iterations ({max_iterations}) reached, stopping simulation")
            break

        # Step 0 - Store old values of components
        c1_old = c1.copy()
        c2_old = c2.copy()
        c3_old = c3.copy()
        c4_old = c4.copy()
        c5_old = c5.copy()

        # Step 1 - Update effective properties    
        sigma = fvutils.gradient_of_scalar(g, phi)
        threshCells = np.ravel(np.argwhere(sigma < eps))
        sigma[threshCells] = 0

        mineralArea = np.sum(sigma * g.cell_volumes)
        mineralVolume = np.sum(phi_total_solid * g.cell_volumes)

        CalciteVol = np.sum(phi_calcite * g.cell_volumes)
        WollastoniteVol = np.sum(phi_wollastonite * g.cell_volumes)
        CSHVol = np.sum(phi_CSH * g.cell_volumes)
        GypsumVol = np.sum(phi_gypsum * g.cell_volumes)

        eps_vol = abs(CalciteVol + WollastoniteVol + CSHVol + GypsumVol - mineralVolume)

        # Check mass conservation
        assert eps_vol < thresh

        MineralVolumeChange = abs(mineralVolume-MineralVolumeOld)
        relMineralVolumeChange = MineralVolumeChange / MineralVolumeOld if MineralVolumeOld > 0 else 0

        # Step 1a - Update flow if required
        if relMineralVolumeChange > UpdateFlow:
            print('Update flow at time', t)
            d["porosity field"] = phi
            p, u, q = flowSolver.SimpleAlgorithm(g, d, bndf, s, p, u)
            U1, U1_bound = ade.discretize_advection(g, bndt, q, c1_bound)
            U2, U2_bound = ade.discretize_advection(g, bndt, q, c2_bound)
            U3, U3_bound = ade.discretize_advection(g, bndt, q, c3_bound)
            U4, U4_bound = ade.discretize_advection(g, bndt, q, c4_bound)
            U5, U5_bound = ade.discretize_advection(g, bndt, q, c5_bound)
            MineralVolumeOld = mineralVolume

        # Calculate reaction enhancement factor
        psi = 4 * phi * phi_total_solid
        sigma_enhanced = sigma * psi

        # Step 2 - Discretize diffusion
        A1, A1_bound = ade.discretize_diffusion(g, bndt, diff*phi, c1_bound)
        A2, A2_bound = ade.discretize_diffusion(g, bndt, diff*phi, c2_bound)
        A3, A3_bound = ade.discretize_diffusion(g, bndt, diff*phi, c3_bound)
        A4, A4_bound = ade.discretize_diffusion(g, bndt, diff*phi, c4_bound)
        A5, A5_bound = ade.discretize_diffusion(g, bndt, diff*phi, c5_bound)
        
        # Step 3 - Solve transport equations
        M = sps.diags(phi * g.cell_volumes / dt)

        # Component 1 - H⁺ (affected by all dissolution and precipitation reactions)
        a = M + A1 + U1
        b = U1_bound + A1_bound + phi * g.cell_volumes / dt * c1_old
        c1 = scipy.sparse.linalg.spsolve(a, b)

        # Component 2 - Ca²⁺ (produced by dissolution, consumed by precipitation)
        a = M + A2 + U2  
        b = U2_bound + A2_bound + phi * g.cell_volumes / dt * c2_old
        c2 = scipy.sparse.linalg.spsolve(a, b)

        # Component 3 - SO₄²⁻ (conservative, from H₂SO₄ ionization)
        a = M + A3 + U3
        b = U3_bound + A3_bound + phi * g.cell_volumes / dt * c3_old
        c3 = scipy.sparse.linalg.spsolve(a, b)

        # Component 4 - Si (produced by wollastonite dissolution, consumed by CSH precipitation)
        a = M + A4 + U4
        b = U4_bound + A4_bound + phi * g.cell_volumes / dt * c4_old
        c4 = scipy.sparse.linalg.spsolve(a, b)

        # Component 5 - CO₂ (produced by calcite dissolution)
        a = M + A5 + U5
        b = U5_bound + A5_bound + phi * g.cell_volumes / dt * c5_old
        c5 = scipy.sparse.linalg.spsolve(a, b)

        # Step 4 - Chemical speciation and reaction rates
        # For simplicity, assume direct relationships between components and species
        cH = c1
        cCa = c2  
        cSO4 = c3
        cSi = c4
        cCO2 = c5
        
        # Calculate carbonate concentration from CO2 equilibrium (simplified)
        # CO₂ + H₂O ⇌ 2H⁺ + CO₃²⁻
        cCO3 = cCO2 * 1e-10 / (cH * cH + 1e-20)  # Simplified equilibrium
        
        # Calculate silicic acid concentration (simplified)
        cH4SiO4 = cSi  # Assume all Si is in H₄SiO₄ form

        # Step 5 - Calculate dissolution rates
        # 修复：使用正确的反应速率公式，参考bioMineralization.py

        # Calcite dissolution rate (pH dependent)
        # CaCO₃ + 2H⁺ → Ca²⁺ + CO₂ + H₂O
        if t >= startDissolutionTime:
            # 修复：只有存在方解石的地方才能溶解
            # 使用表面积sigma而不是sigma_enhanced，避免过度增强
            rDiss_calcite = np.where(phi_calcite > 1e-10,
                                   kappaDiss_calcite * sigma * (cH**2) * VmCalcite,
                                   0)
        else:
            rDiss_calcite = np.zeros(g.num_cells)

        # Wollastonite dissolution rate (pH dependent)
        # CaSiO₃ + 2H⁺ → Ca²⁺ + H₂SiO₃
        if t >= startDissolutionTime:
            # 修复：只有存在硅灰石的地方才能溶解
            # 使用psi因子来考虑反应性表面积
            psi_wollastonite = np.where(phi_wollastonite > 1e-10, 1.0, 0.0)
            rDiss_wollastonite = kappaDiss_wollastonite * sigma * psi_wollastonite * (cH**2) * VmWollastonite
        else:
            rDiss_wollastonite = np.zeros(g.num_cells)

        # Step 6 - Calculate precipitation rates

        # CSH precipitation (calcium silicate hydrate)
        # Ca²⁺ + H₄SiO₄ → CaSiO₃ + 2H₂O + 2H⁺
        Ksp_CSH = 1e-10  # Simplified Ksp for CSH
        SI_CSH = (gammaCa * cCa * gammaSiO2 * cH4SiO4) / Ksp_CSH

        # Check if CSH precipitation conditions are met
        maxSI_CSH = np.amax(SI_CSH)
        if maxSI_CSH > 1 and not precipitation_CSH:
            precipitation_CSH = True
            print(f'CSH precipitation starts at time {t}, max SI = {maxSI_CSH}')

        if precipitation_CSH:
            # 修复：使用更简单稳定的沉淀速率计算
            # 限制沉淀速率以防止数值不稳定
            kappaPrec_CSH_effective = np.maximum(0, kappaPrec_CSH * (SI_CSH - 1))

            # 限制最大沉淀速率以保持数值稳定性
            max_prec_rate = 0.01 / dt  # 每时间步最多沉淀1%体积分数
            kappaPrec_CSH_effective = np.minimum(kappaPrec_CSH_effective, max_prec_rate)

            rPrec_CSH = sigma * kappaPrec_CSH_effective * VmWollastonite
        else:
            rPrec_CSH = np.zeros(g.num_cells)

        # Gypsum precipitation
        # Ca²⁺ + SO₄²⁻ + 2H₂O → CaSO₄·2H₂O
        SI_gypsum = (gammaCa * cCa * gammaSO4 * cSO4) / Ksp_gypsum

        # Check if gypsum precipitation conditions are met
        maxSI_gypsum = np.amax(SI_gypsum)
        if maxSI_gypsum > 1 and not precipitation_gypsum:
            precipitation_gypsum = True
            print(f'Gypsum precipitation starts at time {t}, max SI = {maxSI_gypsum}')

        if precipitation_gypsum:
            # 修复：使用更稳定的石膏沉淀速率计算
            kappaPrec_gypsum_effective = np.maximum(0, kappaPrec_gypsum * (SI_gypsum - 1))

            # 限制最大沉淀速率以保持数值稳定性
            max_prec_rate = 0.01 / dt  # 每时间步最多沉淀1%体积分数
            kappaPrec_gypsum_effective = np.minimum(kappaPrec_gypsum_effective, max_prec_rate)

            rPrec_gypsum = sigma * kappaPrec_gypsum_effective * VmGypsum
        else:
            rPrec_gypsum = np.zeros(g.num_cells)

        # Step 7 - Update mineral volume fractions with stability controls

        # 调试输出：监测反应速率
        if t > 0 and int(t) % 10 == 0 and abs(t - int(t)) < dt:  # 每10秒输出一次
            max_rDiss_wollastonite = np.max(rDiss_wollastonite)
            max_rPrec_gypsum = np.max(rPrec_gypsum)
            max_sigma = np.max(sigma)
            max_cH = np.max(cH)
            print(f"调试 t={t:.1f}s: max_rDiss_wollastonite={max_rDiss_wollastonite:.2e}, max_rPrec_gypsum={max_rPrec_gypsum:.2e}")
            print(f"  max_sigma={max_sigma:.2e}, max_cH={max_cH:.2e}")

        # Calculate proposed changes
        dphi_calcite = -dt * rDiss_calcite
        dphi_wollastonite = -dt * rDiss_wollastonite
        dphi_CSH = dt * rPrec_CSH
        dphi_gypsum = dt * rPrec_gypsum

        # Apply changes with constraints
        phi_calcite += dphi_calcite
        phi_wollastonite += dphi_wollastonite
        phi_CSH += dphi_CSH
        phi_gypsum += dphi_gypsum

        # Ensure non-negative mineral fractions
        phi_calcite = np.maximum(phi_calcite, 0)
        phi_wollastonite = np.maximum(phi_wollastonite, 0)
        phi_CSH = np.maximum(phi_CSH, 0)
        phi_gypsum = np.maximum(phi_gypsum, 0)

        # Calculate total solid fraction
        phi_total_solid = phi_calcite + phi_wollastonite + phi_CSH + phi_gypsum

        # Check if total solid fraction exceeds 1 (porosity would be negative)
        overfill_cells = phi_total_solid > 1.0
        if np.any(overfill_cells):
            print(f"Warning: Overfill detected in {np.sum(overfill_cells)} cells at time {t}")

            # Scale down all mineral fractions proportionally in overfilled cells
            scale_factor = np.ones(g.num_cells)
            scale_factor[overfill_cells] = 0.99 / phi_total_solid[overfill_cells]

            phi_calcite *= scale_factor
            phi_wollastonite *= scale_factor
            phi_CSH *= scale_factor
            phi_gypsum *= scale_factor

            # Recalculate total solid fraction
            phi_total_solid = phi_calcite + phi_wollastonite + phi_CSH + phi_gypsum

        # Update porosity
        phi = 1 - phi_total_solid

        # Final safety checks with more informative error messages
        if np.any(phi < 0):
            negative_cells = np.where(phi < 0)[0]
            min_phi = np.min(phi)
            print(f"Error: Negative porosity {min_phi} in {len(negative_cells)} cells")
            print(f"Max solid fraction: {np.max(phi_total_solid)}")
            print(f"Time: {t}, dt: {dt}")
            # Force correction
            phi = np.maximum(phi, 1e-6)
            phi_total_solid = 1 - phi

        if np.any(phi > 1):
            print(f"Warning: Porosity > 1 detected, correcting...")
            phi = np.minimum(phi, 1.0)
            phi_total_solid = 1 - phi

        # Ensure all constraints are satisfied
        assert np.all(phi >= 0), f"Negative porosity: min={np.min(phi)}"
        assert np.all(phi <= 1), f"Porosity > 1: max={np.max(phi)}"
        assert np.all(phi_total_solid >= 0), f"Negative solid fraction: min={np.min(phi_total_solid)}"
        assert np.all(phi_total_solid <= 1), f"Solid fraction > 1: max={np.max(phi_total_solid)}"

        # Step 8 - Monitor and store results
        if int(t * 100) % 50 == 0:  # Store data every 0.5 time units (更频繁的监测)
            print(f'time {t}')

            l_time.append(int(t))
            l_surf.append(mineralArea)
            l_vol.append(mineralVolume)

            # Calculate average concentrations in pore space
            phiV = phi * g.cell_volumes
            pore_cells = np.where(phi > 1e-6)[0]  # Cells with significant porosity

            if len(pore_cells) > 0:
                den = np.sum(phiV[pore_cells])
                if den > 1e-12:
                    avgH = np.sum(cH[pore_cells] * phiV[pore_cells]) / den
                    avgCa = np.sum(cCa[pore_cells] * phiV[pore_cells]) / den
                    avgSO4 = np.sum(cSO4[pore_cells] * phiV[pore_cells]) / den
                    avgSi = np.sum(cSi[pore_cells] * phiV[pore_cells]) / den
                    avgCO2 = np.sum(cCO2[pore_cells] * phiV[pore_cells]) / den
                else:
                    avgH = avgCa = avgSO4 = avgSi = avgCO2 = 0
            else:
                avgH = avgCa = avgSO4 = avgSi = avgCO2 = 0

            l_H.append(avgH)
            l_Ca.append(avgCa)
            l_SO4.append(avgSO4)
            l_Si.append(avgSi)
            l_CO2.append(avgCO2)

            l_calcite.append(CalciteVol)
            l_wollastonite.append(WollastoniteVol)
            l_CSH.append(CSHVol)
            l_gypsum.append(GypsumVol)

            print(f'Average H⁺: {avgH:.2e}, pH: {-np.log10(avgH*gammaH):.2f}')
            print(f'Average Ca²⁺: {avgCa:.2e}')
            print(f'Average SO₄²⁻: {avgSO4:.2e}')
            print(f'Average Si: {avgSi:.2e}')
            print(f'Average CO₂: {avgCO2:.2e}')
            print(f'Calcite volume: {CalciteVol:.6f}')
            print(f'Wollastonite volume: {WollastoniteVol:.6f}')
            print(f'CSH volume: {CSHVol:.6f}')
            print(f'Gypsum volume: {GypsumVol:.6f}')
            print(f'Total mineral volume: {mineralVolume:.6f}')
            print('---')

        t += dt

    # Prepare output data
    l = {
        "time": l_time,
        "mineral surface": l_surf,
        "mineral volume": l_vol,
        "sum H": l_H,
        "sum Ca": l_Ca,
        "sum SO4": l_SO4,
        "sum Si": l_Si,
        "sum CO2": l_CO2,
        "calcite volume": l_calcite,
        "wollastonite volume": l_wollastonite,
        "CSH volume": l_CSH,
        "gypsum volume": l_gypsum,
    }

    # Final concentrations (5 components)
    c = np.vstack([c1, c2, c3, c4, c5])

    print("=== 硫酸矿化反应模拟完成 ===")
    print(f"最终时间: {t}")
    print(f"最终矿物体积: {mineralVolume}")
    print(f"最终孔隙度范围: {np.min(phi):.3f} - {np.max(phi):.3f}")
    print("=====================================")

    return p, u, c, phi, l
