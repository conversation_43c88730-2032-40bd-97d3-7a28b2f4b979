import sys
import os

# 添加src目录到系统路径
script_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(script_dir, "src")
sys.path.insert(0, src_path)

print("当前工作目录:", os.getcwd())
print("脚本目录:", script_dir)
print("src路径:", src_path)
print("系统路径:", sys.path[:3])

try:
    from geometry import structured
    print("✓ 成功导入 geometry.structured")
except ImportError as e:
    print("✗ 导入 geometry.structured 失败:", e)

try:
    from geometry.bc import BoundaryCondition
    print("✓ 成功导入 geometry.bc")
except ImportError as e:
    print("✗ 导入 geometry.bc 失败:", e)

try:
    from models import flowSolver
    print("✓ 成功导入 models.flowSolver")
except ImportError as e:
    print("✗ 导入 models.flowSolver 失败:", e)

print("测试完成") 