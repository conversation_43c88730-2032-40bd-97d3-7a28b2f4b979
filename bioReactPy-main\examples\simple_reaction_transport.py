"""
简化的反应传输模型示例
演示如何从零开始构建类似bioMineralization.py的模型

这个示例包含：
1. 单组分反应传输
2. 简单的化学反应
3. Newton-Raphson求解
4. 基本的时间步进

学习目标：
- 理解反应传输模型的基本结构
- 掌握Newton-Raphson方法的实现
- 学会组织复杂的数值模拟代码
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse import diags
from scipy.sparse.linalg import spsolve

class SimpleGrid:
    """简单的1D网格类"""
    def __init__(self, Nx, Lx):
        self.Nx = Nx
        self.Lx = Lx
        self.dx = Lx / Nx
        self.x = np.linspace(self.dx/2, Lx - self.dx/2, Nx)  # 单元中心
        self.cell_volumes = np.ones(Nx) * self.dx

class SimpleReactionTransport:
    """简化的反应传输求解器"""
    
    def __init__(self, grid, parameters):
        self.g = grid
        self.params = parameters
        
        # 提取参数
        self.D = parameters['diffusion_coefficient']
        self.u = parameters['velocity'] 
        self.k_reaction = parameters['reaction_rate_constant']
        self.dt = parameters['time_step']
        
    def discretize_diffusion(self, c_boundary_left, c_boundary_right):
        """离散化扩散项 -D * d²c/dx²"""
        Nx = self.g.Nx
        dx = self.g.dx
        
        # 构建三对角矩阵
        main_diag = -2 * self.D / dx**2 * np.ones(Nx)
        off_diag = self.D / dx**2 * np.ones(Nx-1)
        
        # 边界条件处理
        main_diag[0] += self.D / dx**2  # 左边界
        main_diag[-1] += self.D / dx**2  # 右边界
        
        A = diags([off_diag, main_diag, off_diag], [-1, 0, 1], format='csr')
        
        # 边界条件右端项
        b_boundary = np.zeros(Nx)
        b_boundary[0] = self.D / dx**2 * c_boundary_left
        b_boundary[-1] = self.D / dx**2 * c_boundary_right
        
        return A, b_boundary
    
    def discretize_advection(self, c_boundary_left):
        """离散化对流项 -u * dc/dx (上风格式)"""
        Nx = self.g.Nx
        dx = self.g.dx
        
        if self.u > 0:  # 从左到右流动
            main_diag = self.u / dx * np.ones(Nx)
            lower_diag = -self.u / dx * np.ones(Nx-1)
            U = diags([lower_diag, main_diag], [-1, 0], format='csr')
            
            # 边界条件
            b_boundary = np.zeros(Nx)
            b_boundary[0] = self.u / dx * c_boundary_left
        else:
            # 处理从右到左的流动
            main_diag = -self.u / dx * np.ones(Nx)
            upper_diag = self.u / dx * np.ones(Nx-1)
            U = diags([main_diag, upper_diag], [0, 1], format='csr')
            b_boundary = np.zeros(Nx)
            
        return U, b_boundary
    
    def chemical_equilibrium(self, c_total, temperature=298.15):
        """
        求解简单的化学平衡：A ⇌ B + C
        
        平衡常数：K = [B][C]/[A]
        质量守恒：[A] + [B] = c_total
        假设：[B] = [C]（化学计量比1:1）
        
        因此：K = [B]²/([A]) = [B]²/(c_total - [B])
        
        这导致二次方程：[B]² + K*[B] - K*c_total = 0
        """
        # 温度相关的平衡常数（简化的van't Hoff方程）
        K = 1e-4 * np.exp(-1000/temperature)  # 示例平衡常数
        
        # 求解二次方程的系数
        a = 1.0
        b = K  
        c = -K * c_total
        
        # 二次公式求解
        discriminant = b**2 - 4*a*c
        if discriminant < 0:
            return 0.0, c_total  # 无实数解，返回初始状态
            
        cB = (-b + np.sqrt(discriminant)) / (2*a)  # 取正根
        cA = c_total - cB
        
        return cA, cB
    
    def reaction_rate(self, cA, cB, c_total):
        """
        计算反应速率
        
        反应：A → B + C (不可逆)
        速率：r = k * [A] * (1 - [B]/[B]_eq)
        """
        # 计算平衡浓度
        cA_eq, cB_eq = self.chemical_equilibrium(c_total)
        
        # 计算反应速率（包含平衡抑制项）
        if cB_eq > 0:
            rate = self.k_reaction * cA * (1 - cB/cB_eq)
        else:
            rate = self.k_reaction * cA
            
        return max(0, rate)  # 确保速率非负
    
    def solve_timestep(self, c_old, c_boundary_left, c_boundary_right):
        """求解一个时间步"""
        Nx = self.g.Nx
        
        # 离散化算子
        A_diff, b_diff = self.discretize_diffusion(c_boundary_left, c_boundary_right)
        A_adv, b_adv = self.discretize_advection(c_boundary_left)
        
        # 时间离散化矩阵（隐式欧拉）
        I = diags([np.ones(Nx)], [0], format='csr')
        A_time = I - self.dt * (A_diff + A_adv)
        
        # 右端项（包含旧时间步的值和边界条件）
        b_time = c_old + self.dt * (b_diff + b_adv)
        
        # 求解线性方程组
        c_new = spsolve(A_time, b_time)
        
        return c_new
    
    def solve(self, c_initial, t_end, c_boundary_left, c_boundary_right):
        """主求解循环"""
        Nx = self.g.Nx
        Nt = int(t_end / self.dt)
        
        # 初始化解数组
        c = c_initial.copy()
        
        # 存储结果用于后处理
        c_history = [c.copy()]
        t_history = [0.0]
        
        print(f"开始求解，总时间步数：{Nt}")
        
        for n in range(Nt):
            t = (n + 1) * self.dt
            
            # 求解传输方程
            c = self.solve_timestep(c, c_boundary_left, c_boundary_right)
            
            # 应用化学反应（算子分裂法）
            for i in range(Nx):
                # 在每个网格单元中求解化学平衡
                cA, cB = self.chemical_equilibrium(c[i])
                
                # 计算反应速率
                rate = self.reaction_rate(cA, cB, c[i])
                
                # 更新浓度（简化的反应项处理）
                c[i] = c[i] + self.dt * rate
            
            # 存储结果
            if n % 10 == 0:  # 每10步存储一次
                c_history.append(c.copy())
                t_history.append(t)
                print(f"时间步 {n+1}/{Nt}, t = {t:.3f}s, 最大浓度 = {np.max(c):.6f}")
        
        return np.array(c_history), np.array(t_history)

def main():
    """主函数：设置和运行简单的反应传输模拟"""
    
    # 网格参数
    Nx = 100
    Lx = 1.0  # 1米长的域
    grid = SimpleGrid(Nx, Lx)
    
    # 物理参数
    parameters = {
        'diffusion_coefficient': 1e-9,  # m²/s
        'velocity': 1e-6,               # m/s
        'reaction_rate_constant': 1e-5, # 1/s
        'time_step': 100.0              # s
    }
    
    # 创建求解器
    solver = SimpleReactionTransport(grid, parameters)
    
    # 初始条件：高斯分布
    x = grid.x
    c_initial = np.exp(-(x - 0.3)**2 / (2 * 0.05**2))
    
    # 边界条件
    c_boundary_left = 0.0
    c_boundary_right = 0.0
    
    # 求解
    t_end = 10000.0  # 总时间10000秒
    c_history, t_history = solver.solve(c_initial, t_end, c_boundary_left, c_boundary_right)
    
    # 可视化结果
    plt.figure(figsize=(12, 8))
    
    # 浓度分布随时间的变化
    plt.subplot(2, 2, 1)
    for i in range(0, len(t_history), max(1, len(t_history)//5)):
        plt.plot(x, c_history[i], label=f't = {t_history[i]:.0f}s')
    plt.xlabel('位置 (m)')
    plt.ylabel('浓度')
    plt.title('浓度分布随时间变化')
    plt.legend()
    plt.grid(True)
    
    # 中心点浓度随时间变化
    plt.subplot(2, 2, 2)
    center_idx = Nx // 2
    plt.plot(t_history, c_history[:, center_idx])
    plt.xlabel('时间 (s)')
    plt.ylabel('中心点浓度')
    plt.title('中心点浓度时间序列')
    plt.grid(True)
    
    # 总质量随时间变化（质量守恒检查）
    plt.subplot(2, 2, 3)
    total_mass = np.sum(c_history * grid.dx, axis=1)
    plt.plot(t_history, total_mass)
    plt.xlabel('时间 (s)')
    plt.ylabel('总质量')
    plt.title('质量守恒检查')
    plt.grid(True)
    
    # 最终浓度分布
    plt.subplot(2, 2, 4)
    plt.plot(x, c_initial, 'b--', label='初始')
    plt.plot(x, c_history[-1], 'r-', label='最终')
    plt.xlabel('位置 (m)')
    plt.ylabel('浓度')
    plt.title('初始vs最终分布')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('simple_reaction_transport_results.png', dpi=300)
    plt.show()
    
    print(f"\n模拟完成！")
    print(f"初始总质量: {total_mass[0]:.6f}")
    print(f"最终总质量: {total_mass[-1]:.6f}")
    print(f"质量变化: {(total_mass[-1] - total_mass[0])/total_mass[0]*100:.2f}%")

if __name__ == "__main__":
    main()
