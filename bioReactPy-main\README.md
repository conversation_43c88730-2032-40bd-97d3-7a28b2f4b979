# bioReactPy
A simulation tool for bio-reactive flow in porous media.

BioReactPy currently has the following features:

- Discretization of the Stokes equations of flow;

- Coupled flow and reactive transport, based on the microcontinuum approach;

- Fully coupled reactive transport and biomass-nutrient growth treated with Monod equation.

For more information, see the provided benchmarks and application tests.

BioReactPy is developed by <PERSON>, Hydrogeology Group (GHS) at the Universitat Politecnica de Catalunya (UPC), Barcelona, Spain. The software is developed under the <PERSON>C<PERSON> grant agreement No. 101063414 – CO2FOREARM Fundamental study of CO2 storage through microbially enhanced carbon mineralization.

## CITING

If you use BioReactPy in your research, we ask you to cite the following publication

Starnoni M., Dawi M. A., Sanchez-Vila X. (2024). BioReactPy: An open-source software for simulation of microbial-mediated reactive processes in porous media, Applied Computing and Geosciences, [doi: 10.1016/j.acags.2024.100166](https://doi.org/10.1016/j.acags.2024.100166).


## RELATED PUBLICATIONS

Star<PERSON>i, M., <PERSON>, M<PERSON>, & <PERSON>, <PERSON>. (2024). BioReactPy: An open-source software for simulation of microbial-mediated reactive processes in porous media. Applied Computing and Geosciences, 22, 100166. [doi: 10.1016/j.acags.2024.100166](https://doi.org/10.1016/j.acags.2024.100166)

Starnoni, M., & Sanchez-Vila, X. (2024). Pore-scale modelling of subsurface biomineralization for carbon mineral storage. Advances in Water Resources, 185, 104641. [doi: 10.1016/j.advwatres.2024.104641](https://doi.org/10.1016/j.advwatres.2024.104641)

Starnoni, M., Sanchez‐Vila, X., Recalcati, C., Riva, M., & Guadagnini, A. (2024). Process modeling of mineral dissolution from nano‐Scale surface topography observations. Geophysical Research Letters, 51(16), e2024GL110030. [doi: 10.1029/2024GL110030](https://doi.org/10.1029/2024GL110030)

Dawi, M. A., Starnoni, M., Porta, G., & Sanchez-Vila, X. (2024). Pore-scale coupling of flow, biofilm growth, and nutrient transport: A microcontinuum approach. Water Resources Research, 60, e2024WR038393 [doi:10.1029/2024WR038393](https://doi.org/10.1029/2024WR038393)

Runscripts for all simulations presented in these papers can be found in the associated Data Availability Statements.

## INSTALLATION
Installation of BioReactPy is straightforward: simply download from github.
## GETTING STARTED
Confer to the examples and application tests.
